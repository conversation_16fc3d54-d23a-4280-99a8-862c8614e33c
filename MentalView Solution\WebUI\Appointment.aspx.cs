﻿using Data;
using Newtonsoft.Json;
using Resources;
using Syncfusion.JavaScript.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using System.IO;
using Syncfusion.DocToPDFConverter;
using Syncfusion.Pdf;
using System.Web.Services;
using Syncfusion.JavaScript.Web;
using System.Data;
using System.Threading;
using System.Drawing;
using static Data.MentalViewDataSet;
using System.Runtime.InteropServices.ComTypes;
using Microsoft.Ajax.Utilities;
using Syncfusion.Linq;
using System.Diagnostics;
using System.Globalization;
using System.Web.UI.HtmlControls;
using System.Configuration;

namespace WebUI
{

    public partial class Appointment : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = null;
                DataTable dt;

                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                #region  Controls Localization
                //this.startTimeTxtBox.DatePickerButtonText = new ButtonText() { Today = GlobalResources.TodayText, TimeNow = GlobalResources.TimeNowText, Done = GlobalResources.DoneText, TimeTitle = GlobalResources.TimeText };
                this.startDateTxtBox.ButtonText = GlobalResources.TodayText;
                this.startDateTxtBox.DateFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortDatePattern;
                this.startTimeTxtBox.TimeFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortTimePattern;
                this.endTimeTxtBox.TimeFormat = System.Threading.Thread.CurrentThread.CurrentUICulture.DateTimeFormat.ShortTimePattern;

                #endregion

                if (CookieHandler.GetAuthCookie(Page) == null)
                {
                    Response.Redirect("Login.aspx");
                    return;
                }
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 userId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }

                #region  Data Bindings
                DropDownListItem ddlItem;

                #region ContactId
                DataTable contactsDT = Data.Business.ContactsBusiness.GetAllContactsList(tenantId);
                DataRow emptyContactsRow = contactsDT.NewRow();
                emptyContactsRow.ItemArray = new object[] { -1, " ", " " };
                contactsDT.Rows.InsertAt(emptyContactsRow, 0);
                this.contactIdCmbBox.DataSource = contactsDT;
                this.contactIdCmbBox.DataBind();
                #endregion

                //#region  StateDDL
                //if (this.stateDDL.Items.Count == 0)
                //{
                //    this.stateDDL.Items.Clear();
                //    ddlItem = new DropDownListItem();
                //    ddlItem.Text = "&nbsp;";
                //    ddlItem.Value = "";
                //    this.stateDDL.Items.Add(ddlItem);

                //    DataTable statesDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentStates"];

                //    foreach (DataRow statesRow in statesDT.Rows)
                //    {
                //        ddlItem = new DropDownListItem();
                //        ddlItem.Text = statesRow["Text"].ToString();
                //        ddlItem.Value = statesRow["Value"].ToString();
                //        this.stateDDL.Items.Add(ddlItem);
                //    }
                //}
                //#endregion

                #region  TherapistId
                if (this.therapistIdCmbBox.Items.Count == 0)
                {
                    this.therapistIdCmbBox.Items.Clear();
                    ddlItem = new DropDownListItem();
                    ddlItem.Text = "&nbsp;";
                    ddlItem.Value = "-1";
                    this.therapistIdCmbBox.Items.Add(ddlItem);

                    MentalViewDataSet doctorsDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
                    foreach (DataRow doctorRow in doctorsDS.Users.Rows)
                    {
                        ddlItem = new DropDownListItem();
                        ddlItem.Text = doctorRow["FullName"].ToString();
                        ddlItem.Value = doctorRow["UserId"].ToString();
                        this.therapistIdCmbBox.Items.Add(ddlItem);
                    }
                }
                #endregion

                #region  IntervetionModel
                //this.intervetionModelDDL.Items.Clear();
                //ddlItem = new DropDownListItem();
                //ddlItem.Text = "&nbsp;";
                //ddlItem.Value = "-1";
                //this.intervetionModelDDL.Items.Add(ddlItem);

                //DataTable intervetionModelsDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-IntervetionModels"];

                //foreach (DataRow intervetionModelsRow in intervetionModelsDT.Rows)
                //{
                //    ddlItem = new DropDownListItem();
                //    ddlItem.Text = intervetionModelsRow["Text"].ToString();
                //    ddlItem.Value = intervetionModelsRow["Value"].ToString();
                //    this.intervetionModelDDL.Items.Add(ddlItem);
                //}
                if (this.intervetionModelDDL.Items.Count == 0)
                {
                    dt = Data.FieldValuesMappings.DataSet.Tables["Appointments-IntervetionModels"];
                    this.intervetionModelDDL.DataSource = dt;
                    this.intervetionModelDDL.DataBind();
                }
                #endregion

                #region  PaymentType
                this.paymentTypeDDL.Items.Clear();
                ddlItem = new DropDownListItem();
                ddlItem.Text = "&nbsp;";
                ddlItem.Value = "-1";
                ddlItem.Selected = "True";
                this.paymentTypeDDL.Items.Add(ddlItem);

                DataTable paymentTypeDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-PaymentTypes"];

                foreach (DataRow paymentTypeRow in paymentTypeDT.Rows)
                {
                    ddlItem = new DropDownListItem();
                    ddlItem.Text = paymentTypeRow["Text"].ToString();
                    ddlItem.Value = paymentTypeRow["Value"].ToString();
                    this.paymentTypeDDL.Items.Add(ddlItem);
                }
                #endregion

                #region  InterventionTechniques
                if (this.intervetionTechniquesDDL.Items.Count == 0)
                {
                    dt = Data.FieldValuesMappings.DataSet.Tables["Appointments-IntervetionTechniques"];
                    this.intervetionTechniquesDDL.DataSource = dt;
                    this.intervetionTechniquesDDL.DataBind();
                }
                #endregion

                #region Rooms
                this.roomDDL.Items.Clear();

                DataTable roomsDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"].Copy();
                if (roomsDT.Columns.Contains("HtmlAttr") == false)
                {
                    roomsDT.Columns.Add("HtmlAttr", typeof(string));
                }

                DataRow roomsRow = roomsDT.NewRow();
                roomsRow["RoomName"] = " ";
                roomsRow["RoomId"] = "";
                roomsRow["BackColor"] = "";
                roomsRow["FontColor"] = "";
                roomsDT.Rows.InsertAt(roomsRow, 0);

                foreach (DataRow roomRow in roomsDT.Rows)
                {
                    roomRow["htmlAttr"] = "style='background-color:" + roomRow["BackColor"].ToString() + "'";
                }
                this.roomDDL.DataSource = roomsDT;
                #endregion


                #endregion

                if (!IsPostBack)
                {
                    //Αν δημιουργούμε νέο Appointment
                    if (string.IsNullOrEmpty(this.Request.Params["AppointmentId"]))
                    {
                        ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;
                        Data.Business.ConfigureDataSet(ref ds);

                        this.initialSupervisionRequest.Value = "False";

                        //Δημιουργεί το νεο Appointment
                        MentalViewDataSet.AppointmentsRow appointmentsRow = ds.Appointments.NewAppointmentsRow();
                        appointmentsRow.TenantId = tenantId;
                        appointmentsRow.AppointmentType = "Appointment";  //Ορίζουμε ότι είναι τύπος 'Συνεδρία'
                        appointmentsRow.StartTime = DateTime.Now;
                        appointmentsRow.VisibleToAll = false;
                        appointmentsRow.BlockOther = false;
                        appointmentsRow.EndTime = appointmentsRow.StartTime.AddHours(1);
                        ds.Appointments.AddAppointmentsRow(appointmentsRow);

                        ViewState["Appointment"] = ds;
                    }
                    else  //Αν κάνουμε επεξεργασία
                    {
                        int appointmentId = int.Parse(this.Request.Params["AppointmentId"]);

                        ds = (Data.MentalViewDataSet)Data.Business.AppointmentsBusiness.GetAppointmentById(appointmentId);
                        this.originalStartTimeHiddenField.Value = ds.Appointments[0].StartTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                        this.originalEndTimeHiddenField.Value = ds.Appointments[0].EndTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments

                        this.initialSupervisionRequest.Value = ds.Appointments[0].SupervisionRequest.ToString();

                        //Αν το appointment που κάνουμε edit έχει ήδη CustomRecurrence.
                        if (ds.Appointments[0].CustomRecurrence)
                        {
                            ViewState["CustomRecurrenceExists"] = true;  //Καταχωρούμε τοπικά ότι το appointment υπό επεξεργασία έχει ήδη recurrence.
                        }
                        else  //Αν το appointment που κάνουμε edit ΔΕΝ έχει CustomRecurrence.
                        {
                            //Ελέγχουμε μήπως έχει το "παλιό" recurrence (της Syncfusion).
                            //Ο παρακάτω κώδικας υπάρχει για να μετατρέπει το recurrence της Syncfusion στο νέο CustomRecurrence.
                            if (ds.Appointments[0].Recurrence == true)
                            {
                                ds.Appointments[0].CustomRecurrence = ds.Appointments[0].Recurrence;
                                ds.Appointments[0].CustomRecurrenceRule = ds.Appointments[0].RecurrenceRule;

                                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, "H συνεδρία είναι επαναλαμβανόμενη με το παλιό τρόπο επανάληψης. Κάντε αποθήκευση της συνεδρίας ώστε η επαναληψιμότητα να μετατραπεί στο νέο τρόπο.", ServerMessageButtons.Ok);
                                //Προσοχή: αυτός ο κώδικας εμφανίζει MessageBox και σταματάει προκαλεί reload, ίσως ο παρακάτω κώδικας να μην εκτελείται.
                            }
                        }

                        ViewState["Appointment"] = ds;
                    }

                    this.SetDataOnUIControls(ds);
                }
                else  //Αν γίνεται postback
                {
                    ds = (Data.MentalViewDataSet)ViewState["Appointment"];

                    if (Request.Form["__EVENTTARGET"] == this.contactIdCmbBox.ID)
                    {
                        try
                        {
                            this.contactIdCmbBox.Value = Request.Form["ctl00$ctl00$includeFilesBody$mainBody$contactIdCmbBox"];
                            this.GetDataFromUIControls(ref ds);

                            this.SetDataOnUIControls(ds);
                        }
                        catch (Exception exp)
                        {
                            Data.ExceptionLogger.LogException(exp);
                            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
                        }
                    }
                }

                this.SetCancellationControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void SetCancellationControls(MentalViewDataSet ds)
        {
            this.cancelBtn.Disabled = (!(ds.Appointments[0].AppointmentId > 0)) || ds.Appointments[0].Canceled;  //Ενεργοποιούμε το Cancel button μόνο το Appointment δεν είναι καινούριο ή αν είναι ήδη ακυρωμένο.
            if (ds.Appointments[0].ChargeableCancellation == false)
            {
                this.chargeableCanceledInfoLbl.Text = this.GetLocalResourceObject("AppointmentIsNoChargeable").ToString();
                this.toggleChargableCanceledAppointmentLnk.Text = this.GetLocalResourceObject("ChangeToChargeable").ToString();
            }
            else
            {
                this.chargeableCanceledInfoLbl.Text = this.GetLocalResourceObject("AppointmentIsChargeable").ToString();
                this.toggleChargableCanceledAppointmentLnk.Text = this.GetLocalResourceObject("ChangeToNoChargeable").ToString();
            }
            this.canceledAppointmentFieldsDiv.Visible = ds.Appointments[0].Canceled;
            this.cancelUpcomingAppointmentsDiv.Visible = ds.Appointments[0].CustomRecurrence == true;

        }

        private void SetDataOnUIControls(Data.MentalViewDataSet ds)
        {
            this.appointmentIdHiddenField.Value = ds.Appointments[0].AppointmentId.ToString();

            if (ds.Appointments[0].IsContactIdNull())
            {
                this.contactIdCmbBox.Value = "";
            }
            else
            {
                this.contactIdCmbBox.Value = ds.Appointments[0].ContactId.ToString();
            }

            if (ds.Appointments[0].IsUserIdNull())
            {
                this.therapistIdCmbBox.Value = "";
            }
            else
            {
                this.therapistIdCmbBox.Value = ds.Appointments[0].UserId.ToString();
            }

            this.startDateTxtBox.Value = ds.Appointments[0].StartTime;
            this.startTimeTxtBox.Value = ds.Appointments[0].StartTime.ToString("HH:mm:ss");
            this.endTimeTxtBox.Value = ds.Appointments[0].EndTime.ToString("HH:mm:ss");


            if (ds.Appointments[0].Room != "")
            {
                this.roomDDL.Value = ds.Appointments[0].Room;
            }
            else
            {
                this.roomDDL.Value = "";
            }

            if (ds.Appointments[0].IsUserIdNull() == false)
            {
                this.therapistIdCmbBox.Value = ds.Appointments[0].UserId.ToString();
            }
            this.requestTxtBox.Text = ds.Appointments[0].Request;
            this.supervisorInstructionsBeforeTxtBox.Text = ds.Appointments[0].SupervisorInstructionsBefore;
            this.supervisorCommentsAfterTxtBox.Text = ds.Appointments[0].SupervisorCommentsAfter;

            //if (ds.Appointments[0].IntervetionModel != "")
            //{
            //    this.intervetionModelDDL.Value = ds.Appointments[0].IntervetionModel;
            //}
            //else
            //{
            //    this.intervetionModelDDL.Value = "";
            //}
            this.SetValueOnSelectControl(this.intervetionModelDDL, ds.Appointments[0].IntervetionModel);

            //this.intervetionTechniquesTxtbox.Text = ds.Appointments[0].IntervetionTechniques;
            this.SetValueOnSelectControl(this.intervetionTechniquesDDL, ds.Appointments[0].IntervetionTechniques);
            this.therapistCommentsTxtBox.Text = ds.Appointments[0].TherapistComments;
            this.supervisionRequestChkBox.Checked = ds.Appointments[0].SupervisionRequest;
            this.priceTxtBox.Value = ds.Appointments[0].Price.ToString(CultureInfo.InvariantCulture);
            this.deductionsTxtBox.Value = ds.Appointments[0].Deductions.ToString(CultureInfo.InvariantCulture);
            this.paymentTypeDDL.Value = ds.Appointments[0].PaymentType;
            this.bankTxtBox.Value = ds.Appointments[0].Bank;
            this.notesTxtBox.Text = ds.Appointments[0].Notes;

            ////Αν το appointment έχει το παλιό Recurrence μηχανισμό.
            //if (ds.Appointments[0].Recurrence)
            //{
            //    this.recurrenceChkBox.Checked = ds.Appointments[0].Recurrence;
            //    this.recurrenceValueHiddenField.Value = ds.Appointments[0].RecurrenceRule;
            //}
            //else  //αλλιώς έχει το δικό μου recurrence μηχανισμό
            //{
            //    this.recurrenceValueHiddenField.Value = ds.Appointments[0].CustomRecurrenceRule;
            //    this.recurrenceChkBox.Checked = ds.Appointments[0].CustomRecurrenceId!="";
            //}

            this.recurrenceChkBox.Checked = ds.Appointments[0].CustomRecurrence;
            this.recurrenceValueHiddenField.Value = ds.Appointments[0].CustomRecurrenceRule;

            //Αν το Appointment είχε αρχικά CustomRecurrence.
            if (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == true)
            {
                this.recurrenceChkBox.Disabled = true;
                this.recurrenceEditorWrapper.Visible = false;

                string recurrencesDatesText = ds.Appointments[0]["RecurrencesStartDates"].ToString().Replace("<Expr1>", "").Replace("</Expr1>", "").Replace("Expr1>", "");  //Αφαιρούμε τα tags που εισάγει αυτοματα το SQL query λόγω του XML function που χρησιμοποιεί.
                string[] recurrencesDatesTextArray = recurrencesDatesText.Split(new char[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
                recurrencesDatesTextArray = recurrencesDatesTextArray.Select(d => Convert.ToDateTime(d).ToString("ddd dd/MM/yyyy HH:mm")).ToArray();
                this.recurrenceInfoLbl.Text = string.Format(this.GetLocalResourceObject("RecurrenceInfoMessage").ToString(), string.Join(",&nbsp;&nbsp;&nbsp;", recurrencesDatesTextArray));
            }
            else  //Αν το Appointment δεν είχε αρχικά recurrence.
            {
                this.recurrenceChkBox.Disabled = false;
                this.recurrenceEditorWrapper.Visible = true;
                this.recurrenceInfoLbl.Text = "";
            }
        }

        private void GetDataFromUIControls(ref MentalViewDataSet ds)
        {
            try
            {
                if (this.contactIdCmbBox.Value != "-1" && this.contactIdCmbBox.Value != "" && this.contactIdCmbBox.Value != null)
                {
                    ds.Appointments[0].ContactId = Convert.ToInt64(this.contactIdCmbBox.Value);
                }
                else
                {
                    ds.Appointments[0].SetContactIdNull();
                }

                if (this.therapistIdCmbBox.Value != "-1" && this.therapistIdCmbBox.Value != "" && this.therapistIdCmbBox.Value != null)
                {
                    ds.Appointments[0].UserId = Convert.ToInt64(this.therapistIdCmbBox.Value);
                }
                else
                {
                    ds.Appointments[0].SetUserIdNull();
                }

                DateTime startDate = this.startDateTxtBox.Value.Value;
                TimeSpan startTimeSpan = TimeSpan.Parse(this.startTimeTxtBox.Value);
                TimeSpan endTimeSpan = TimeSpan.Parse(this.endTimeTxtBox.Value);
                ds.Appointments[0].StartTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, startTimeSpan.Hours, startTimeSpan.Minutes, 0, 0);
                ds.Appointments[0].EndTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, endTimeSpan.Hours, endTimeSpan.Minutes, 0, 0);




                if (this.roomDDL.Value != "" && this.roomDDL.Value != null)
                {
                    ds.Appointments[0].Room = this.roomDDL.Value;
                }
                else
                {
                    ds.Appointments[0].Room = "";
                }

                ds.Appointments[0].Request = this.requestTxtBox.Text;
                ds.Appointments[0].SupervisorInstructionsBefore = this.supervisorInstructionsBeforeTxtBox.Text;
                ds.Appointments[0].SupervisorCommentsAfter = this.supervisorCommentsAfterTxtBox.Text;

                //if (this.intervetionModelDDL.Value != "" && this.intervetionModelDDL.Value != null)
                //{
                //    ds.Appointments[0].IntervetionModel = this.intervetionModelDDL.Value;
                //}
                //else
                //{
                //    ds.Appointments[0].IntervetionModel = "";
                //}
                ds.Appointments[0].IntervetionModel = this.RetrieveSelect2ValueFromPageForm(this.intervetionModelDDL.ID);

                //ds.Appointments[0].IntervetionTechniques = this.intervetionTechniquesTxtbox.Text;
                ds.Appointments[0].IntervetionTechniques = this.RetrieveSelect2ValueFromPageForm(this.intervetionTechniquesDDL.ID);
                ds.Appointments[0].TherapistComments = this.therapistCommentsTxtBox.Text;
                ds.Appointments[0].SupervisionRequest = this.supervisionRequestChkBox.Checked;
                ds.Appointments[0].Price = Convert.ToDecimal(this.priceTxtBox.Value, CultureInfo.InvariantCulture);
                ds.Appointments[0].Deductions = Convert.ToDecimal(this.deductionsTxtBox.Value, CultureInfo.InvariantCulture);
                ds.Appointments[0].PaymentType = this.paymentTypeDDL.Value == null ? "" : this.paymentTypeDDL.Value;
                ds.Appointments[0].Bank = this.bankTxtBox.Value;
                ds.Appointments[0].Notes = this.notesTxtBox.Text;

                //ds.Appointments[0].CustomRecurrenceRule = this.recurrenceChkBox.Checked ? this.recurrenceValueHiddenField.Value : "";

                //Αν το Appointment είχε CustomRecurrence αρχικά.
                if (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == true)
                {

                }
                else  //Αν το Appointment δεν είχε CustomRecurrence αρχικά.
                {
                    ds.Appointments[0].CustomRecurrence = this.recurrenceChkBox.Checked;
                    ds.Appointments[0].CustomRecurrenceRule = this.recurrenceChkBox.Checked ? this.recurrenceValueHiddenField.Value : "";
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveCloseBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(false);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Save(bool close)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 userId = Convert.ToInt64(userData["UserId"]);

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                ds.EnforceConstraints = false;
                MentalViewDataSet initialValuesDS = (MentalViewDataSet)ds.Copy();
                initialValuesDS.RejectChanges();

                if (this.ValidateControls() == true)
                {
                    bool newAppointment = ds.Appointments[0].RowState == DataRowState.Added;  //Καταχωρεί αν ειναι νέο Appointment ή επεξεργασία.

                    //#region Ο παρακάτω κώδικας καταγράφει ποιες στήλες άλλαξαν τιμή, μόλις καλέσουμε την GetDataFromUIControls()
                    //List<string> columnsChanged = new List<string>();
                    //ds.Appointments.ColumnChanging += (object sender, DataColumnChangeEventArgs e) => { if(e.ProposedValue.ToString() != ds.Appointments[0][e.Column.ColumnName].ToString())
                    //    {
                    //        columnsChanged.Add(e.Column.ColumnName);
                    //    }};

                    //#endregion
                    this.GetDataFromUIControls(ref ds);
                    ViewState["Appointment"] = ds;

                    //Αν το Appointment ΔΕΝ είχε CustomRecurrence αρχικά.
                    if (ViewState["CustomRecurrenceExists"] == null || (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == false))
                    {
                        //Αν υπάρχει CustomRecurrence.
                        if (ds.Appointments[0].CustomRecurrence)
                        {
                            //Αφού το appointment είναι recurrent τότε ελέγχει να μην διαρκούν όλα τα appointment περισσότερο από ένα έτος ή να μην ξεπερνάνε τις 50 επαναλήψεις.
                            if (this.ValidateRecurrence(ds.Appointments[0].CustomRecurrenceRule, ds.Appointments[0].StartTime) == false)
                            {
                                ViewState["Appointment"] = ds;
                                this.SetDataOnUIControls(ds);
                                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ExceedingAppointmentRecurrenceMessage").ToString(), ServerMessageButtons.Ok);
                                return;
                            }

                            #region Εμφανίζει το μήνυμα επιβεβαίωσης για το recurrent appointment
                            //Βρίσκει πόσες επαναλήψεις θα δημιουργηθούν και σε ποιες ημέρες.
                            List<DateTime> dates = new List<DateTime>();
                            dates = Data.RecurrenceHelper.GetRecurrenceDateTimeCollection(ds.Appointments[0].CustomRecurrenceRule, ds.Appointments[0].StartTime).ToList();
                            string datesText = "";
                            foreach (DateTime date in dates)
                            {
                                datesText += date.ToString("dddd dd MMM yyyy ") + "<br/>";
                            }

                            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, string.Format(GetLocalResourceObject("SaveRecurrentAppointmentConfirmationMessage").ToString(), datesText), ServerMessageButtons.YesNo, "Save", close.ToString());
                            return;
                            #endregion
                        }
                    }
                    //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                    else if (ViewState["CustomRecurrenceExists"] != null && Convert.ToBoolean(ViewState["CustomRecurrenceExists"]) == true)  //Αν το Appointment ΈΧΕΙ CustomRecurrence αρχικά (δηλαδή κάνουμε επεξεργασία).
                    {
                        //Αν υπάρχει CustomRecurrence.
                        if (this.originalStartTimeHiddenField.Value != "")
                        {
                            DateTime originalStartTime = Convert.ToDateTime(this.originalStartTimeHiddenField.Value);
                            DateTime originalEndTime = Convert.ToDateTime(this.originalEndTimeHiddenField.Value);

                            //Αν οι ημερομηνίες Έναρξης ή Λήξης έχουν αλλάγξει από το χρήστη.
                            //if (originalStartTime != ds.Appointments[0].StartTime || originalEndTime != ds.Appointments[0].EndTime)
                            //Βγήκε το if γιατί θέλουμε να κάνει την παρακάτω ερώτηση ακόμα και αν αλλάξουν και άλλα πεδία, δηλαδή να ρωτάει πάντα.
                            {
                                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("UpdateUpcomingRecurrentAppointmentsConfirmationMessage").ToString(), ServerMessageButtons.YesNoCancel, "SaveIncludeUpcoming", close.ToString());
                                return;
                            }
                        }
                    }

                    Data.Business.SaveAllData(ds);
                    ViewState["Appointment"] = ds;

                    this.originalStartTimeHiddenField.Value = ds.Appointments[0].StartTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                    this.originalEndTimeHiddenField.Value = ds.Appointments[0].EndTime.ToString();  //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments

                    string host = System.Configuration.ConfigurationManager.AppSettings["EmailHost"];
                    int port = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["EmailPort"]);
                    string username = System.Configuration.ConfigurationManager.AppSettings["EmailAcountUsername"];
                    string password = System.Configuration.ConfigurationManager.AppSettings["EmailAcountPassword"];
                    bool useDefaultCredentials = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailUseDefaultCredentials"]);
                    bool enableSsl = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailEnableSsl"]);
                    string senderEmail = System.Configuration.ConfigurationManager.AppSettings["SenderEmail"];
                    string senderName = System.Configuration.ConfigurationManager.AppSettings["SenderName"];
                    string htmlSignature = System.Configuration.ConfigurationManager.AppSettings["EmailHtmlSignature"];

                    //Αν ο χρήστης που κάνει την επεξεργασία είναι διαφορετικός από τον θεραπευτή της συνεδρίας.
                    if (ds.Appointments[0].UserId != userId)
                    {
                        //Στέλνει email στον Θεραπευτή ότι άλλαξε η συνεδρία.
                        EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                        emailManager.SendSUpdateAppointmentNotificationToTherapist(ds, initialValuesDS, newAppointment);
                    }

                    //Αν ενεργοποιήθηκε το Αίτημα για Εποπτεία.
                    if (this.initialSupervisionRequest.Value == "False" && ds.Appointments[0].SupervisionRequest == true)
                    {
                        //Διαβάζουμε το Email του Παναγιωτόπουλου και βάζουμε το Id καρφωτά, γιατί αυτό το email θα στέλνεται μόνο στον Παναγιωτόπουλο.
                        //string[] emails = new string[] { Business.UsersBusiness.GetUserById(9).Users[0].Email };

                        //Στέλνει email στον Θεραπευτή ότι άλλαξε η συνεδρία.
                        EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                        emailManager.SendAppointmentSupervisionRequestNotificationToTherapists(new string[] { "9" }, ds);
                    }

                    if (close)
                    {
                        ViewState.Remove("Appointment");
                        Response.Redirect(@"~\Appointments.aspx");
                    }
                    else
                    {
                        this.SetDataOnUIControls(ds);
                    }
                }
                else
                {
                    this.GetDataFromUIControls(ref ds);
                    //Data.Business.SaveAllData(ds);
                    ViewState["Appointment"] = ds;

                    ////Αν ο χρήστης που κάνει την επεξεργασία είναι διαφορετικός από τον θεραπευτή της συνεδρίας.
                    //if (ds.Appointments[0].UserId != userId)
                    //{
                    //    //Στέλνει email στον Θεραπευτή ότι άλλαξε η συνεδρία.
                    //    EmailManager.SendSUpdateAppointmentNotificationToTherapist(ds, initialValuesDS);
                    //}
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateRecurrence(string recurrenceRule, DateTime startDate)
        {
            //Αν δεν περιέχει καν τη λέξη COUNT, δηλαδή δεν έχει τέλος του recurrence
            if (recurrenceRule.Contains("COUNT") == false)
            {
                return false;
            }
            else
            {
                List<DateTime> dates = new List<DateTime>();
                //Pass the recurrencerule string and the start date of the appointment to this method that returns the dates collection based on the recurrence rule
                dates = Data.RecurrenceHelper.GetRecurrenceDateTimeCollection(recurrenceRule, startDate).ToList();

                if ((dates[dates.Count - 1] - dates[0]).TotalDays > 365)  //Αν η διάρκεια των επαναλήψεων ξεπερνάει τις 365 ημέρες
                {
                    return false;
                }
                else if (dates.Count > 50)  //Αν οι επαναλήψεις ξεπερνάνε τις 50
                {
                    return false;
                }
                else
                {
                    return true;
                }
            }
        }

        protected void closeBtn_Click(object sender, EventArgs e)
        {
            ViewState.Remove("Appointment");
            Response.Redirect(@"Appointments.aspx");
        }

        protected void deleteBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];

                //Αν το appointment είναι νέο ή αν είναι υπάρχον αλλά δεν έχει recurrency.
                if (ds.Appointments[0].RowState == DataRowState.Added || ((ds.Appointments[0].RowState == DataRowState.Modified || ds.Appointments[0].RowState == DataRowState.Unchanged) && ds.Appointments[0].CustomRecurrence == false))
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete");
                }
                else
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteWithUpcomingRecurrencesConfirmationMessage, ServerMessageButtons.YesNoCancel, "DeleteWithUpcomingRecurrences");
                }

                this.GetDataFromUIControls(ref ds);
                ViewState["Appointment"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Delete()
        {
            Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

            ds.Appointments[0].Delete();
            Data.Business.SaveAllData(ds);

            ViewState.Remove("Appointment");
            Response.Redirect(@"Appointments.aspx");
        }

        private void DeleteWithUpcomingRecurrences()
        {
            Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

            //Βρίσκει όλα τα υπόλοιπα recurrent appointments που ανήκουν στο συγκεκριμένο recurrence και συμβαίνουν μετά το επιλεγμένο appointment.
            Data.MentalViewDataSet upcomingAppointmentsDT = Data.Business.AppointmentsBusiness.GetUpcomingRecurrentAppointment(ds.Appointments[0].StartTime, ds.Appointments[0].CustomRecurrenceId);

            ds.Appointments.Merge(upcomingAppointmentsDT.Appointments); //Ενώνει τα επόμενα recurrent appointments με το επιλεγμένο appointment (στη φόρμα).
            foreach (DataRow appointmentRow in ds.Appointments.Rows)
            {
                appointmentRow.Delete();
            }
            Data.Business.SaveAllData(ds);

            ViewState.Remove("Appointment");
            Response.Redirect(@"Appointments.aspx");
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        this.Delete();
                    }
                }
                else if (args.Action == "DeleteWithUpcomingRecurrences")
                {
                    //Αν ο χρήστης επέλεξε να διαγράψει το τρέχον appointment μαζί με αυτά που ακολουθούν.
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        this.DeleteWithUpcomingRecurrences();
                    }
                    else if (args.ButtonClicked == ButtonClicked.No)  //Αν ο χρήστης επέλεξε να διαγράψει το τρέχον appointment μαζί με αυτά που ακολουθούν.
                    {
                        this.Delete();
                    }
                }
                else if (args.Action == "Save")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        //Η αποθήκευση παρακάτω είναι μόνο για όταν πρόκειται να δημιουργηθούν recurrent appointemnts.
                        //Δημιουργεί τα recurrent appointments
                        //ΕΞΗΓΗΣΗ: το ds.Appointments[0] κανονικά είναι το αρχικό appointment που πάει να φτιάξει το πρόγραμμα (που δεν είναι επαναλαμβανόμενο)
                        //όμως τώρα θα πάρει την ημερομηνία του πρώτου από τα recurrent appointments και μετά θα προστεθούν τα άλλα.

                        //Διαγράφει το παλιό recurrence μηχανισμό
                        ds.Appointments[0].Recurrence = false;
                        ds.Appointments[0].RecurrenceRule = "";
                        ds.Appointments[0].RecurrenceExDate = "";
                        ds.Appointments[0].SetRecurrenceIdNull();

                        string guid = Guid.NewGuid().ToString();
                        List<DateTime> dates = new List<DateTime>();
                        dates = Data.RecurrenceHelper.GetRecurrenceDateTimeCollection(ds.Appointments[0].CustomRecurrenceRule, ds.Appointments[0].StartTime).ToList();
                        foreach (DateTime date in dates)
                        {
                            AppointmentsRow newAppointmentsRow = ds.Appointments.NewAppointmentsRow();
                            //newAppointmentsRow = (AppointmentsRow)ds.Appointments.ImportRow(ds.Appointments.Rows[0]);
                            newAppointmentsRow.ItemArray = ds.Appointments.Rows[0].ItemArray.Clone() as object[];
                            newAppointmentsRow.StartTime = new DateTime(date.Year, date.Month, date.Day, newAppointmentsRow.StartTime.Hour, newAppointmentsRow.StartTime.Minute, newAppointmentsRow.StartTime.Second);
                            newAppointmentsRow.EndTime = new DateTime(date.Year, date.Month, date.Day, newAppointmentsRow.EndTime.Hour, newAppointmentsRow.EndTime.Minute, newAppointmentsRow.EndTime.Second);
                            newAppointmentsRow.CustomRecurrence = ds.Appointments[0].CustomRecurrence;
                            newAppointmentsRow.CustomRecurrenceRule = ds.Appointments[0].CustomRecurrenceRule;
                            newAppointmentsRow.CustomRecurrenceId = guid;
                            ds.Appointments.AddAppointmentsRow(newAppointmentsRow);
                        }

                        ds.Appointments[0].Delete();

                        Data.Business.SaveAllData(ds);
                        ViewState["Appointment"] = ds;

                        bool close = true;  //Convert.ToBoolean(args);
                        if (close)
                        {
                            ViewState.Remove("Appointment");
                            Response.Redirect(@"~\Appointments.aspx");
                        }
                    }
                }
                //TODO: Feature:Ενημέρωση ημέρας/ώρας και στα upcoming appointments
                else if (args.Action == "SaveIncludeUpcoming")
                {
                    bool close = Convert.ToBoolean(args.Tag);

                    if (args.ButtonClicked != ButtonClicked.Cancel)  //Αν ο χρήστης επέλεξε είτε να γίνουν update τα upcoming appointmetns είτε οχι (δηλαδή δεν πάτησε ακύρωση).
                    {
                        if (args.ButtonClicked == ButtonClicked.Yes)
                        {
                            //Η αποθήκευση παρακάτω είναι μόνο για όταν πρόκειται να κάνει update το τρέχον appointment μαζί με τα επόμενα recurrent appointments.

                            this.GetDataFromUIControls(ref ds);

                            DateTime initialStartTime = Convert.ToDateTime(ds.Appointments[0]["StartTime", DataRowVersion.Original]);
                            DateTime currentStartTime = Convert.ToDateTime(ds.Appointments[0]["StartTime"]);
                            int daysDiff = Convert.ToInt32(Math.Ceiling((currentStartTime.Date - initialStartTime.Date).TotalDays));
                            DayOfWeek initialDayOfWeek = initialStartTime.DayOfWeek;

                            //Βρίσκει όλα τα υπόλοιπα recurrent appointments που ανήκουν στο συγκεκριμένο recurrence και συμβαίνουν μετά το επιλεγμένο appointment.
                            //Προσθέτουμε μια ημέρα για να μην φέρει το query το ίδιο το ραντεβού που κάνουμε επεξεργασία.
                            Data.MentalViewDataSet upcomingAppointmentsDT = Data.Business.AppointmentsBusiness.GetUpcomingRecurrentAppointment(ds.Appointments[0].StartTime.AddDays(1), ds.Appointments[0].CustomRecurrenceId);

                            ds.Appointments.Merge(upcomingAppointmentsDT.Appointments, true); //Ενώνει τα επόμενα recurrent appointments με το επιλεγμένο appointment (στη φόρμα).
                            //ΣΗΜΕΙΩΣΗ: Βάλαμε true στη 2η παράμετρο ReserveChange γιατί το παραπάνω command που φέρνει τα upcomingAppointmentsDT μπορεί να επιστρέφει το ίδιο το appointment που κάνουμε επεξεργασία
                            //άλλα με τις παλιές τιμές και να πανωγράψει το DataRow που επεξεργαζόμαστε (οπότε έτσι οι αλλαγές μας χάνονται) και μετά όλα τα επόμενα επαναλαμβανόμενα ραντεβού δεν δέχονται καμία αλλαγή 
                            //αφού και στο δικό μας χάθηκαν οι αλλαγές.
                            foreach (MentalViewDataSet.AppointmentsRow appointmentRow in ds.Appointments.Rows)
                            {
                                //Για κάθε μελλοντικό appointment εκτός από αυτό που κάνει επεξεργασία ο χρήστης στην σελίδα, (στο οποίο έχουν ήδη περάσει οι αλλαγές στις ημερομηνίες)
                                if (appointmentRow.AppointmentId != ds.Appointments[0].AppointmentId)
                                {
                                    //Αν στο μελλοντικό appointment η ημέρα είναι ίδια με αυτή που αλλάξαμε (αρχική τιμή) στο appointment υπό επεξεργασία. Θέλουμε να αλλάξουμε μόνο τα ραντεβού που
                                    //ήταν ίδια ημέρα της εβδομάδας με αυτό που αλλάξαμε.
                                    if (appointmentRow.StartTime.DayOfWeek == initialDayOfWeek)
                                    {
                                        appointmentRow.StartTime = appointmentRow.StartTime.AddDays(daysDiff).Date.Add(ds.Appointments[0].StartTime.TimeOfDay);
                                        appointmentRow.EndTime = appointmentRow.EndTime.AddDays(daysDiff).Date.Add(ds.Appointments[0].EndTime.TimeOfDay);
                                    }

                                    //Μεταφέρει τις αλλαγές από τα άλλα πεδία από το appointment υπό επεξεργασία στα recurrent appointment.
                                    //appointmentRow.AppointmentCategoryId = ds.Appointments[0].AppointmentCategoryId;
                                    //appointmentRow.AppointmentType = ds.Appointments[0].AppointmentType;
                                    appointmentRow.Room = ds.Appointments[0].Room;
                                    appointmentRow.ContactId = ds.Appointments[0].ContactId;
                                    appointmentRow.UserId = ds.Appointments[0].UserId;
                                    appointmentRow.Description = ds.Appointments[0].Description;
                                    //appointmentRow.AllDay = ds.Appointments[0].AllDay;
                                    appointmentRow.Price = ds.Appointments[0].Price;
                                    appointmentRow.PaymentType = ds.Appointments[0].PaymentType;
                                    appointmentRow.Bank = ds.Appointments[0].Bank;
                                    appointmentRow.Deductions = ds.Appointments[0].Deductions;
                                    appointmentRow.Notes = ds.Appointments[0].Notes;
                                }
                            }
                        }
                        else if (args.ButtonClicked == ButtonClicked.No)
                        {
                            //Η αποθήκευση παρακάτω είναι μόνο για όταν πρόκειται να κάνει update  MONO το τρέχον appointment (ΌΧΙ τα επόμενα recurrent appointments).

                            this.GetDataFromUIControls(ref ds);
                        }

                        Data.Business.SaveAllData(ds);
                        ViewState["Appointment"] = ds;

                        if (close)
                        {
                            ViewState.Remove("Appointment");
                            Response.Redirect(@"~\Appointments.aspx");
                        }
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                //Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        //private bool ValidateData(Data.MentalViewDataSet.AppointmentsRow appointmentsRow)
        //{
        //    try
        //    {
        //        //Αν το πεδίο UserId (
        //        if (appointmentsRow.IsUserIdNull() == true)
        //        {
        //            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeLaterThanEndTimeMessage").ToString(), ServerMessageButtons.Ok, "");
        //            return false;
        //        }

        //        //Αν η ημερομηνία Άφιξη είναι μεταγενέστερη της Αναχώρησης
        //        if (appointmentsRow.StartTime > appointmentsRow.EndTime)
        //        {
        //            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeLaterThanEndTimeMessage").ToString(), ServerMessageButtons.Ok, "");
        //            return false;
        //        }

        //        return true;
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        throw;
        //    }
        //}

        private bool ValidateControls()
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                Int64 tenantId = ds.Appointments[0].TenantId;
                Int64 appointmentId = ds.Appointments[0].AppointmentId;

                //Επαφή
                if (this.contactIdCmbBox.Value == null || this.contactIdCmbBox.Value == "" || this.contactIdCmbBox.Value == "-1")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν το πεδίο UserId (
                if (this.therapistIdCmbBox.Value == null || this.therapistIdCmbBox.Value == "" || this.therapistIdCmbBox.Value == "-1")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("TherapistRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ημερομηνία Άφιξη είναι null
                if (this.startTimeTxtBox.Value == null)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ημερομηνία Αναχώρηση είναι null
                if (this.endTimeTxtBox.Value == null)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EndTimeRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ώρα Έναρξης είναι μεταγενέστερη της Λήξης
                TimeSpan startTimeTimeSpan = TimeSpan.Parse(this.startTimeTxtBox.Value);
                TimeSpan endTimeTimeSpan = TimeSpan.Parse(this.endTimeTxtBox.Value);

                if (startTimeTimeSpan > endTimeTimeSpan)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeLaterThanEndTimeMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν η ώρα Έναρξης είναι πριν τις 8:00 το πρωί (έχουμε ρυθμίσεις να μην φαίνεται στο ημερολόγιο)
                if (startTimeTimeSpan < new TimeSpan(8, 0, 0))
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("StartTimeBeforeEightMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }


                DateTime startDate = this.startDateTxtBox.Value.Value;
                TimeSpan startTimeSpan = TimeSpan.Parse(this.startTimeTxtBox.Value);
                TimeSpan endTimeSpan = TimeSpan.Parse(this.endTimeTxtBox.Value);
                DateTime startDateTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, startTimeSpan.Hours, startTimeSpan.Minutes, 0, 0);
                DateTime endDateTime = new DateTime(startDate.Year, startDate.Month, startDate.Day, endTimeSpan.Hours, endTimeSpan.Minutes, 0, 0);
                Int64 selectedUserId = Convert.ToInt64(this.therapistIdCmbBox.Value);

                # region  Ελέγχει αν το Room είναι κατειλημμένο 
                if (this.roomDDL.Value != "" && this.roomDDL.Value != null)
                {
                    DataTable roomsDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"].Copy();  //Βρίσκει ολα τα διαθεσιμα Rooms.

                    string[] physicalRoomIds = roomsDT.Select("IsPhysical=true").Select(x => x["RoomId"].ToString()).ToArray();  //Βρίσκει μόνο τα Rooms που είναι φυσικός χώρος.

                    //Ψάχνει να βρει ποια Rooms είναι occupied την ώρα του Event.
                    List<string> occupiedRoomIds = Business.AppointmentsBusiness.GetOccupiedRooms(tenantId, appointmentId, selectedUserId, "", physicalRoomIds, startDateTime, endDateTime);

                    //Βρίσκει τα Names των Rooms που είναι occupied
                    List<string> occupiedRoomNames = new List<string>();
                    foreach (string occupiedRoomId in occupiedRoomIds)
                    {
                        occupiedRoomNames.Add(roomsDT.Select("RoomId='" + occupiedRoomId.ToString() + "'")[0]["RoomName"].ToString());
                    }

                    if (occupiedRoomIds.Contains(this.roomDDL.Value))  //Αν το επιλεγμένο Room είναι ανάμεσα στα occupied
                    {
                        string occupiedRooms = String.Join(",", occupiedRoomNames.ToArray());
                        string message = string.Format(GetLocalResourceObject("RoomAlreadyOccupiedMessage").ToString(), occupiedRooms);
                        ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, message, ServerMessageButtons.Ok, "");
                        return false;
                    }
                }
                #endregion

                #region  Ελέγχει αν δεν υπάρχει ήδη άλλο Appointment/Event την ώρα του Event.
                //Ψάχνει να βρει τα Appointments που συμπίπτουν την ώρα του Event.
                List<Int64> conflictingAppointmentsEvents = Business.AppointmentsBusiness.CheckTaskConflicts(tenantId, appointmentId, selectedUserId, "", startDateTime, endDateTime);

                if (conflictingAppointmentsEvents.Count > 0)  //Αν υπάρχουν conflicting Appointments/Events
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("AppointmentConflictsWithOthers").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }
                #endregion

                return true;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void sendServiceEmailToContact_Click(object sender, EventArgs e)
        {
            try
            {
                this.SendUpdateNotificationEmailToTherapist();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void SendUpdateNotificationEmailToTherapist()
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

                MentalViewDataSet initialValuesDS = (MentalViewDataSet)ds.Copy();
                initialValuesDS.RejectChanges();

                //Ενημερώνει το ds με τα στοιχεία του Therapist
                ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);

                //Αν o Therapist έχει email
                if (ds.Appointments[0].UsersRow.Email != "")
                {
                    if (ds.Appointments[0].IsUserIdNull() == false)
                    {
                        //Ενημερώνει το ds με τα στοιχεία του User
                        ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
                    }

                    //Ενημερώνει το ds με τα στοιχεία του AppointmentCategories
                    ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(ds.Appointments[0].TenantId));

                    string host = System.Configuration.ConfigurationManager.AppSettings["EmailHost"];
                    int port = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["EmailPort"]);
                    string username = System.Configuration.ConfigurationManager.AppSettings["EmailAcountUsername"];
                    string password = System.Configuration.ConfigurationManager.AppSettings["EmailAcountPassword"];
                    bool useDefaultCredentials = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailUseDefaultCredentials"]);
                    bool enableSsl = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailEnableSsl"]);
                    string senderEmail = System.Configuration.ConfigurationManager.AppSettings["SenderEmail"];
                    string senderName = System.Configuration.ConfigurationManager.AppSettings["SenderName"];
                    string htmlSignature = System.Configuration.ConfigurationManager.AppSettings["EmailHtmlSignature"];

                    EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                    bool isNewAppointment = ds.Appointments[0].RowState == DataRowState.Added;
                    emailManager.SendSUpdateAppointmentNotificationToTherapist(ds, initialValuesDS, isNewAppointment);

                    this.SetDataOnUIControls(ds);
                }
                else
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ContactHasNoEmailMessage, ServerMessageButtons.Ok, "");
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void sendServiceEmailToUser_Click(object sender, EventArgs e)
        {
            try
            {
                //this.SendServiceEmailToUser();
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        //private void SendServiceEmailToUser()
        //{
        //    try
        //    {
        //        Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];

        //        this.GetDataFromUIControls(ref ds);

        //        //Διαβάζει το email του χρήστη
        //        Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
        //        Int64 userId = Convert.ToInt64(userData["UserId"]);
        //        Data.MentalViewDataSet userDS = Data.Business.UsersBusiness.GetUserById(userId);

        //        if (userDS.Users[0].Email != "")
        //        {
        //            //Ενημερώνει το ds με τα στοιχεία του Contact
        //            ds.Contacts.Merge(Data.Business.ContactsBusiness.GetContactById(ds.Appointments[0].ContactId).Contacts);

        //            //Ενημερώνει το ds με τα στοιχεία του Contact
        //            ds.PredefAppointmentItems.Merge(Data.Business.PredAppointmentItemsBusiness.GetAllPredefAppointmentItemsList(ds.Appointments[0].TenantId));

        //            if (ds.Appointments[0].IsUserIdNull() == false)
        //            {
        //                //Ενημερώνει το ds με τα στοιχεία του User
        //                ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
        //            }

        //            //Ενημερώνει το ds με τα στοιχεία του AppointmentCategories
        //            ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(ds.Appointments[0].TenantId));

        //            EmailManager.SendReservationEmailToContact(userDS.Users[0].Email, ds);

        //            this.SetDataOnUIControls(ds);
        //        }
        //        else
        //        {
        //            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.UserHasNoEmailMessage, ServerMessageButtons.Ok, "");
        //        }
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        //protected void sendServiceEmailToAdmin_Click(object sender, EventArgs e)
        //{
        //    try
        //    {
        //        this.SendServiceEmailToAdmin();
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        //private void SendServiceEmailToAdmin()
        //{
        //    try
        //    {
        //        Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
        //        this.GetDataFromUIControls(ref ds);

        //        //Ενημερώνει το ds με τα στοιχεία του Contact
        //        ds.Contacts.Merge(Data.Business.ContactsBusiness.GetContactById(ds.Appointments[0].ContactId).Contacts);

        //        //Ενημερώνει το ds με τα στοιχεία του Contact
        //        ds.PredefAppointmentItems.Merge(Data.Business.PredAppointmentItemsBusiness.GetAllPredefAppointmentItemsList(ds.Appointments[0].TenantId));

        //        if (ds.Appointments[0].IsUserIdNull() == false)
        //        {
        //            //Ενημερώνει το ds με τα στοιχεία του User
        //            ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
        //        }

        //        //Ενημερώνει το ds με τα στοιχεία του AppointmentCategories
        //        ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(ds.Appointments[0].TenantId));

        //        EmailManager.SendReservationEmailToContact("<EMAIL>", ds);

        //        this.SetDataOnUIControls(ds);
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
        //    }
        //}

        #region  Reports
        private WordDocument PrepareAppointmentReport()
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                //Ενημερώνει το ds με τα στοιχεία του Contact
                ds.Contacts.Merge(Data.Business.ContactsBusiness.GetContactById(ds.Appointments[0].ContactId).Contacts);

                ////Ενημερώνει το ds με τα στοιχεία του AppointmentCategory
                //if (ds.Appointments[0].IsAppointmentCategoryIdNull() == false)
                //{
                //    ds.AppointmentCategories.Merge(Data.Business.AppointmentCategoriesBusiness.GetByAppointmentCategoryId(ds.Appointments[0].AppointmentCategoryId).AppointmentCategories);
                //}

                //Ενημερώνει το ds με τα στοιχεία του TherapistId
                if (ds.Appointments[0].IsUserIdNull() == false)
                {
                    ds.Users.Merge(Data.Business.UsersBusiness.GetUserById(ds.Appointments[0].UserId).Users);
                }

                WordDocument document = new WordDocument();

                //Loads or opens an existing Word document from stream
                FileStream fileStreamPath = new FileStream(Server.MapPath(@"AppointmentReport.docx"), FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                //Loads or opens an existing Word document through Open method of WordDocument class 
                document.Open(fileStreamPath, FormatType.Automatic);

                #region  Εισάγει τα πεδία
                //Στοιχεία επαφής
                document.Replace("[ContactFullName]", ds.Appointments[0].ContactFullName, false, true);
                document.Replace("[StartEndDate]", ds.Appointments[0].StartTime.Date.ToShortDateString() + " " + ds.Appointments[0].StartTime.ToShortTimeString() + " - " + ds.Appointments[0].EndTime.ToShortTimeString(), false, true);
                //if (ds.Appointments[0].IsAppointmentCategoryIdNull() == false)
                //{
                //    document.Replace("[Category]", ds.Appointments[0].AppointmentCategoriesRow.CategoryName, false, true);
                //}
                //else
                //{
                //    document.Replace("[Category]", "", false, true);
                //}

                if (ds.Appointments[0].IsUserIdNull() == false)
                {
                    document.Replace("[TherapistFullName]", ds.Appointments[0].UsersRow.FullName, false, true);
                }
                else
                {
                    document.Replace("[TherapistFullName]", "", false, true);
                }

                document.Replace("[EndTime]", ds.Appointments[0].EndTime.ToString("HH:mm"), false, true);
                document.Replace("[Request]", ds.Appointments[0].Request, false, true);
                //if (ds.Appointments[0].State != "" && ds.Appointments[0].State != "-1")
                //{
                //    document.Replace("[State]", Data.FieldValuesMappings.GetDisplayOfSingleValue("Appointments-AppointmentStates", ds.Appointments[0].State), false, true);
                //}
                //else
                //{
                //    document.Replace("[State]", "", false, true);
                //}
                document.Replace("[SupervisorInstructionsBefore]", ds.Appointments[0].SupervisorInstructionsBefore, false, true);
                //document.Replace("[IntervetionModel]", ds.Appointments[0].IntervetionModel, false, true);
                if (ds.Appointments[0].IntervetionModel != "" && ds.Appointments[0].IntervetionModel != "-1")
                {
                    document.Replace("[IntervetionModel]", Data.FieldValuesMappings.GetDisplayOfSingleValue("Appointments-IntervetionModels", ds.Appointments[0].IntervetionModel), false, true);
                }
                else
                {
                    document.Replace("[IntervetionModel]", "", false, true);
                }
                document.Replace("[TherapistComments]", ds.Appointments[0].TherapistComments, false, true);
                document.Replace("[IntervetionTechniques]", ds.Appointments[0].IntervetionTechniques, false, true);
                document.Replace("[SupervisionRequest]", ds.Appointments[0].SupervisionRequest ? "Ναι" : "Όχι", false, true);
                document.Replace("[SupervisorCommentsAfter]", ds.Appointments[0].SupervisorCommentsAfter, false, true);
                document.Replace("[Notes]", ds.Appointments[0].Notes, false, true);


                //document.Replace("[TV]", (this.ExistInAppointmentItems(ds, "Τηλεόραση") ? "Χ" : ""), false, true);
                //document.Replace("[Internet]", (this.ExistInAppointmentItems(ds, "Internet") ? "Χ" : ""), false, true);
                //document.Replace("[CCTV]", (this.ExistInAppointmentItems(ds, "CCTV") ? "Χ" : ""), false, true);
                //document.Replace("[Alarm]", (this.ExistInAppointmentItems(ds, "Alarm") ? "Χ" : ""), false, true);
                //document.Replace("[AutoRailingDoor]", (this.ExistInAppointmentItems(ds, "Αυτ/σμός Καγκελόπορτας") ? "Χ" : ""), false, true);
                //document.Replace("[DoorCommunication]", (this.ExistInAppointmentItems(ds, "Θυροτηλεόραση") ? "Χ" : ""), false, true);
                //document.Replace("[General]", (this.ExistInAppointmentItems(ds, "Γενικά") ? "Χ" : ""), false, true);


                #endregion

                return document;

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void previewAppointmentReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                //Αν ΔΕΝ είναι συμπληρωμένο το πεδίο του Πελάτη
                if (this.contactIdCmbBox.Value == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                }
                else
                {
                    ViewState["Appointment"] = ds;

                    if (this.ValidateControls())
                    {
                        //Loads an existing Word document
                        WordDocument wordDocument = this.PrepareAppointmentReport();

                        Session["Report"] = wordDocument;
                        Session["ReportName"] = Resources.GlobalResources.AppointmentReportText + " " + ds.Appointments[0].AppointmentId.ToString() + ".pdf";

                        ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);
                    }
                }

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportAppointmentReportToPdfBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                //Αν ΔΕΝ είναι συμπληρωμένο το πεδίο του Πελάτη
                if (this.contactIdCmbBox.Value == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                }
                else
                {
                    ViewState["Appointment"] = ds;

                    if (this.ValidateControls())
                    {
                        //Loads an existing Word document
                        WordDocument wordDocument = this.PrepareAppointmentReport();
                        //Initializes the ChartToImageConverter for converting charts during Word to pdf conversion
                        //wordDocument.ChartToImageConverter =  new ChartToImageConverter();
                        //Creates an instance of the DocToPDFConverter
                        DocToPDFConverter converter = new DocToPDFConverter();
                        //Converts Word document into PDF document
                        PdfDocument pdfDocument = converter.ConvertToPDF(wordDocument);
                        //Saves the PDF file 
                        //pdfDocument.Save("WordtoPDF.pdf");
                        MemoryStream stream = new MemoryStream();
                        //Saves the document to t
                        pdfDocument.Save(Resources.GlobalResources.AppointmentReportText + " " + ds.Appointments[0].AppointmentId.ToString() + ".pdf", Response, HttpReadType.Save);
                        //Closes the instance of document objects
                        pdfDocument.Close(true);
                        wordDocument.Close();
                    }
                }
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportAppointmentReportToWordBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                //Αν ΔΕΝ είναι συμπληρωμένο το πεδίο του Πελάτη
                if (this.contactIdCmbBox.Value == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                }
                else
                {
                    ViewState["Appointment"] = ds;

                    if (this.ValidateControls())
                    {
                        //Loads an existing Word document
                        WordDocument wordDocument = this.PrepareAppointmentReport();
                        //Initializes the ChartToImageConverter for converting charts during Word to pdf conversion

                        MemoryStream stream = new MemoryStream();
                        //Saves the document to t
                        wordDocument.Save(Resources.GlobalResources.AppointmentReportText + " " + ds.Appointments[0].AppointmentId.ToString() + ".docx", FormatType.Docx, Response, HttpContentDisposition.InBrowser);
                        //Closes the instance of document objects
                        wordDocument.Close();
                    }
                }
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #endregion

        protected void showContactBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                if (ds.Appointments[0].ContactId != null && ds.Appointments[0].ContactId >= 0)
                {
                    Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                    string roleName = userData["Role"].ToString();

                    //Αν ο χρήστης είναι Admin
                    if (roleName == "Admin")
                    {
                        Response.Redirect(@"Contact.aspx?ContactId=" + ds.Appointments[0].ContactId.ToString());
                    }
                    else
                    {
                        Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                        Int64 userId = Convert.ToInt64(userData["UserId"]);

                        Business.ContactsBusiness contactsBusiness = new Business.ContactsBusiness();
                        Int64? therapistId = contactsBusiness.GetTherapistIdOfContact(tenantId, ds.Appointments[0].ContactId);

                        //Αν ο χρήστης είναι Therapist και είναι ο ίδιος που έχει το Contact
                        if (therapistId == null || therapistId == userId)
                        {
                            Response.Redirect(@"Contact.aspx?ContactId=" + ds.Appointments[0].ContactId.ToString());
                        }
                        else
                        {
                            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.NoPermissionToViewContactMessage, ServerMessageButtons.Ok);
                            Response.Clear();
                            Response.End();
                            return;
                        }
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void cancelAppointmentBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 userId = Convert.ToInt64(userData["UserId"]);
                bool initialAppointmentChargeableCancellation = false;
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                MentalViewDataSet initialValuesDS = (MentalViewDataSet)ds.Copy();
                initialValuesDS.RejectChanges();
                this.GetDataFromUIControls(ref ds);

                ds.Appointments[0].Canceled = true;
                ds.Appointments[0].ChargeableCancellation = (this.chargeableCancellation.Value.ToLower() == "true") ? true : false;   //Ορίζει αν το appointment στη φόρμα είναι χρώσιμο ή όχι.
                initialAppointmentChargeableCancellation = ds.Appointments[0].ChargeableCancellation;  //Αποθήκευση σε προσωρινή μεταβλητή για να χρησιμοποιηθεί παρακάτω.
                if (ds.Appointments[0].ChargeableCancellation == false)  //Αν η ακύρωση ορίστηκε ως μη χρεώσιμη.
                {
                    ds.Appointments[0].Price = 0;
                    ds.Appointments[0].Deductions = 0;
                }
                this.chargeableCancellation.Value = "";

                //Αν ο χρήστης έχει επιλέξει τα να ακυρωθούν τα επόμενα ραντεβού, τα ακυρώνει αλλά δεν επηρεάζει αν είναι χρεώσιμα.
                if (this.cancelUpcomingAppointmentsChkBox.Checked == true)
                {
                    //Βρίσκει όλα τα υπόλοιπα recurrent appointments που ανήκουν στο συγκεκριμένο recurrence και συμβαίνουν μετά το επιλεγμένο appointment.
                    Data.MentalViewDataSet upcomingAppointmentsDT = Data.Business.AppointmentsBusiness.GetUpcomingRecurrentAppointment(ds.Appointments[0].StartTime, ds.Appointments[0].CustomRecurrenceId);

                    ds.Appointments.Merge(upcomingAppointmentsDT.Appointments); //Ενώνει τα επόμενα recurrent appointments με το επιλεγμένο appointment (στη φόρμα).
                    foreach (MentalViewDataSet.AppointmentsRow appointmentRow in ds.Appointments.Rows)
                    {
                        appointmentRow.Canceled = true;
                        if (initialAppointmentChargeableCancellation == false)  //Αν η ακύρωση ορίστηκε ως μη χρεώσιμη.
                        {
                            appointmentRow.Price = 0;
                            appointmentRow.Deductions = 0;
                        }
                    }
                }

                //this.Save(true);
                Data.Business.SaveAllData(ds);


                string host = System.Configuration.ConfigurationManager.AppSettings["EmailHost"];
                int port = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["EmailPort"]);
                string username = System.Configuration.ConfigurationManager.AppSettings["EmailAcountUsername"];
                string password = System.Configuration.ConfigurationManager.AppSettings["EmailAcountPassword"];
                bool useDefaultCredentials = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailUseDefaultCredentials"]);
                bool enableSsl = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailEnableSsl"]);
                string senderEmail = System.Configuration.ConfigurationManager.AppSettings["SenderEmail"];
                string senderName = System.Configuration.ConfigurationManager.AppSettings["SenderName"];
                string htmlSignature = System.Configuration.ConfigurationManager.AppSettings["EmailHtmlSignature"];

                //Αν ο χρήστης που κάνει την επεξεργασία είναι διαφορετικός από τον θεραπευτή της συνεδρίας.
                if (ds.Appointments[0].UserId != userId)
                {
                    //Στέλνει email στον Θεραπευτή ότι άλλαξε η συνεδρία.
                    EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                    emailManager.SendSUpdateAppointmentNotificationToTherapist(ds, initialValuesDS, false);
                }

                //Αν ενεργοποιήθηκε το Αίτημα για Εποπτεία.
                if (this.initialSupervisionRequest.Value == "False" && ds.Appointments[0].SupervisionRequest == true)
                {
                    //Διαβάζουμε το Email του Παναγιωτόπουλου και βάζουμε το Id καρφωτά, γιατί αυτό το email θα στέλνεται μόνο στον Παναγιωτόπουλο.
                    //string[] emails = new string[] { Business.UsersBusiness.GetUserById(9).Users[0].Email };

                    //Στέλνει email στον Θεραπευτή ότι άλλαξε η συνεδρία.
                    EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                    emailManager.SendAppointmentSupervisionRequestNotificationToTherapists(new string[] { "9" }, ds);
                }

                ViewState.Remove("Appointment");
                Response.Redirect(@"~\Appointments.aspx");
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void toggleChargableCanceledAppointmentLnk_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                ds.Appointments[0].ChargeableCancellation = !ds.Appointments[0].ChargeableCancellation;

                this.SetDataOnUIControls(ds);
                this.SetCancellationControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void setAppointmentNoCanceledLnk_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Appointment"];
                this.GetDataFromUIControls(ref ds);

                ds.Appointments[0].Canceled = false;

                this.SetDataOnUIControls(ds);
                this.SetCancellationControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private string RetrieveSelect2ValueFromPageForm(string controlName)
        {
            try
            {
                controlName = "$" + controlName;  //Βάζουμε το $ μπροστά γιατί όλα τα select2 είναι σε container controls και έχουν κι άλλο όνομα μπροστά.
                return this.RetrieveControlValueFromPageForm(controlName).Replace(",", "|");
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(new ApplicationException("controlId: " + controlName));
                throw exp;
            }
        }

        protected void SetValueOnSelectControl(HtmlSelect selectControl, string value)
        {
            //Καθαρίζει τα selected
            foreach (System.Web.UI.WebControls.ListItem item in selectControl.Items)
            {
                item.Selected = false;
            }

            if (value != null && value.Trim() != "")
            {
                //Κάνει select αυτά που πρέπει
                string[] values = value.Split('|');

                foreach (string s in values)
                {
                    try
                    {
                        selectControl.Items.FindByValue(s).Selected = true;
                    }
                    catch (Exception exp)
                    {
                        Data.ExceptionLogger.LogException(new Exception("Exception in control " + selectControl.ID + ". Value:" + s, exp));
                    }
                }
            }
        }

        private string RetrieveControlValueFromPageForm(string controlId)
        {
            try
            {
                foreach (string key in Request.Form.AllKeys)
                {
                    if (key.EndsWith(controlId) == true)
                    {
                        return Request.Form[key];
                    }
                }

                return "";

                ////Αν βρήκε το control με το controlId στο Request.Form. Αν δεν το βρήκε σημαίνει ότι το select2 δεν έχει τιμή.
                //if (Request.Form.AllKeys.Where(n => n.EndsWith(controlId)).Count() > 0)
                //{
                //    List<string> values = new List<string>();
                //    Request.Form.AllKeys.Where(n => n.EndsWith(controlId)).ToList().ForEach(x => values.Add(Request.Form[x]));

                //    return values[0];
                //}
                //else
                //{
                //    return "";
                //}
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(new ApplicationException("controlId: " + controlId));
                throw exp;
            }
        }

    }
}