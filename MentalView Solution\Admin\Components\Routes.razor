@using Admin.Components.Layout
@using Admin.Components.Pages

<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Program).Assembly">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
                <Authorizing>
                    <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Authorizing...</p>
                        </div>
                    </div>
                </Authorizing>
                <NotAuthorized>
                    <Login />
                </NotAuthorized>
            </AuthorizeRouteView>
            <FocusOnNavigate RouteData="routeData" Selector="h1" />
        </Found>
        <NotFound>
            <PageTitle>Not found</PageTitle>
            <LayoutView Layout="typeof(Layout.MainLayout)">
                <div class="d-flex justify-content-center align-items-center" style="height: 100vh;">
                    <div class="text-center">
                        <h1>404 - Page Not Found</h1>
                        <p role="alert">Sorry, there's nothing at this address.</p>
                        <a href="/" class="btn btn-primary">Go Home</a>
                    </div>
                </div>
            </LayoutView>
        </NotFound>
    </Router>
</CascadingAuthenticationState>
