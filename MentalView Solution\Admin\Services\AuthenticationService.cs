using Microsoft.AspNetCore.Components.Server.ProtectedBrowserStorage;
using Microsoft.AspNetCore.Authentication;
using System.Security.Claims;

namespace Admin.Services
{
    public class AuthenticationService : IAuthenticationService
    {
        private readonly ProtectedSessionStorage _sessionStorage;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private ClaimsPrincipal _currentUser = new ClaimsPrincipal(new ClaimsIdentity());

        // Hardcoded credentials - in production, this should be in a secure configuration
        private readonly Dictionary<string, string> _validCredentials = new()
        {
            { "admin", "admin123" },
            { "superadmin", "super123" }
        };

        public event Action<ClaimsPrincipal> AuthenticationStateChanged = delegate { };

        public AuthenticationService(ProtectedSessionStorage sessionStorage, IHttpContextAccessor httpContextAccessor)
        {
            _sessionStorage = sessionStorage;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task<bool> LoginAsync(string username, string password)
        {
            if (_validCredentials.TryGetValue(username, out var validPassword) && validPassword == password)
            {
                var claims = new List<Claim>
                {
                    new Claim(ClaimTypes.Name, username),
                    new Claim(ClaimTypes.Role, username == "superadmin" ? "SuperAdmin" : "Admin"),
                    new Claim("LoginTime", DateTime.UtcNow.ToString())
                };

                var identity = new ClaimsIdentity(claims, "Cookies");
                _currentUser = new ClaimsPrincipal(identity);

                // Sign in with cookie authentication
                if (_httpContextAccessor.HttpContext != null)
                {
                    await _httpContextAccessor.HttpContext.SignInAsync("Cookies", _currentUser);
                }

                await _sessionStorage.SetAsync("user", username);
                await _sessionStorage.SetAsync("loginTime", DateTime.UtcNow.ToString());

                AuthenticationStateChanged.Invoke(_currentUser);
                return true;
            }

            return false;
        }

        public async Task LogoutAsync()
        {
            // Sign out from cookie authentication
            if (_httpContextAccessor.HttpContext != null)
            {
                await _httpContextAccessor.HttpContext.SignOutAsync("Cookies");
            }

            await _sessionStorage.DeleteAsync("user");
            await _sessionStorage.DeleteAsync("loginTime");

            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
            AuthenticationStateChanged.Invoke(_currentUser);
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            try
            {
                var result = await _sessionStorage.GetAsync<string>("user");
                return result.Success && !string.IsNullOrEmpty(result.Value);
            }
            catch
            {
                return false;
            }
        }

        public async Task<ClaimsPrincipal> GetUserAsync()
        {
            try
            {
                var userResult = await _sessionStorage.GetAsync<string>("user");
                var timeResult = await _sessionStorage.GetAsync<string>("loginTime");

                if (userResult.Success && !string.IsNullOrEmpty(userResult.Value))
                {
                    var username = userResult.Value;
                    var claims = new List<Claim>
                    {
                        new Claim(ClaimTypes.Name, username),
                        new Claim(ClaimTypes.Role, username == "superadmin" ? "SuperAdmin" : "Admin")
                    };

                    if (timeResult.Success && !string.IsNullOrEmpty(timeResult.Value))
                    {
                        claims.Add(new Claim("LoginTime", timeResult.Value));
                    }

                    var identity = new ClaimsIdentity(claims, "Cookies");
                    _currentUser = new ClaimsPrincipal(identity);
                    return _currentUser;
                }
            }
            catch
            {
                // If there's any error, return unauthenticated user
            }

            _currentUser = new ClaimsPrincipal(new ClaimsIdentity());
            return _currentUser;
        }
    }
}
