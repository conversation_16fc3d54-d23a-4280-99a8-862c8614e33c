﻿using Data;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class Default : System.Web.UI.Page
    {
        protected async void Page_Load(object sender, EventArgs e)
        {
            try
            {
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();

                //await ChatGPT();

                if (this.Page.User.Identity.IsAuthenticated == true)
                {
                    Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                    if (userData != null)
                    {
                        Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                        Int64 loggedUserId = Convert.ToInt64(userData["UserId"]);
                        string roleName = userData["Role"].ToString();
                        if (roleName == "Guest")
                        {
                            this.calendarDiv.Visible = false;
                        }
                        DashboardInfo info = Data.Business.GeneralBusiness.GetDashboardInfo(tenantId, loggedUserId);

                        this.contactsCountLbl.Text = info.ContactsCount.ToString();
                        this.futureTasksCountLbl.Text = info.FutureUserAppointmentsCount.ToString();
                    }
                    else
                    {
                        FormsAuthentication.SignOut();
                        Response.Redirect("~/Login.aspx");
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }



        private void Button2_Click(object sender, EventArgs e)
        {
            try
            {
                //RegisterAsyncTask(new PageAsyncTask(ChatGPT));
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private async System.Threading.Tasks.Task ChatGPT()
        {
            try
            {
                // Your OpenAI API key
                string apiKey = "********************************************************************************************************************************************************************";

                // The API endpoint for OpenAI
                string endpoint = "https://api.openai.com/v1/chat/completions";

                // Define the request payload
                var requestBody = new
                {
                    model = "gpt-3.5-turbo", // or "gpt-3.5-turbo" , "gpt-4"
                    messages = new[]
                        {
                new { role = "system", content = "You are a helpful assistant." },
                new { role = "user", content = "Can you tell me about a similar video game like Ghost Recon Breakpoint" }
        },
                    max_tokens = 2000
                };

                // Serialize the request payload to JSON
                string jsonPayload = JsonConvert.SerializeObject(requestBody);

                using (var client = new HttpClient())
                {
                    // Add the authorization header
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

                    // Send the POST request
                    var response = await client.PostAsync(
                            endpoint,
                            new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                        );

                    // Get the response content
                    string responseBody = await response.Content.ReadAsStringAsync();

                    // Process the response (optional)
                    var responseJson = JsonConvert.DeserializeObject(responseBody);
                    Response.Write($"<pre>{JsonConvert.SerializeObject(responseJson, Formatting.Indented)}</pre>");
                }


            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

    }
}
