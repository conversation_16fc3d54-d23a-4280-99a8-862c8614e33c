﻿/* Move down content because we have a fixed navbar that is 50px tall */

.form-control {
    border-radius: 0px;
    border: 1px solid #aaa;
}

/*textarea:not(.comment-textarea) {
    resize: none;
}*/

textarea:not(.comment-textarea), textarea.form-control.comment-textarea {
    resize: vertical !important;
    height: 68px;
    min-height: 34px !important;
}

@media only screen and (max-width: 767px) {
    textarea.form-control.comment-textarea {
        margin-top: 15px !important;
    }
}

.orange-font {
    color: orange;
}

.borderless {
    border: 0 !important;
    border-radius: 0 !important;
    background-color: transparent !important;
}

.navbar-custom-menu > ul > li > a {
    white-space: nowrap;
}

    .navbar-custom-menu > ul > li > a > i {
        float: left;
        margin-top: 3px;
    }

#itemsGrid .e-checkselectall {
    display: none !important;
}

/*Για τα radio buttons*/
input[type="radio"] {
    margin-right: 10px;
    margin-left: 10px;
    vertical-align: text-bottom;
}

/*Για τα active buttons */
.btn-default.active {
    background-color: #d6d7d7 !important;
}

/* Για να μην φαίνονται τα sortings στα Grids*/
.e-grid .e-headercell .e-sortadjust {
    display: none;
}


.e-grid.e-wrap .e-columnheader .e-headercelldiv {
    width: fit-content;
    cursor: default;
}

/*Για να φαίνεται σωστά το μενού του user*/
.main-header .navbar-custom-menu .user-menu > a {
    display: inline-flex;
}

.main-header .navbar-custom-menu .user-menu > a {
    cursor: default !important;
}

/*Για να μην φαίνεται το close στο MessageBox*/
#ejServerMessage_closebutton {
    display: none;
}

/*Διόρθωση στο bootstrap DatePicker*/
.datepicker.datepicker-dropdown.dropdown-menu {
    cursor: default;
}

/*Override το μέγεθος στα info-box στο dashboard*/
.info-box-number {
    font-size: 40px !important;
}


/* Wrapping element */
/* Set some basic padding to keep content from hitting the edges */
.body-content {
    padding-left: 15px;
    padding-right: 15px;
}

.required-mark {
    color: orange;
    background: none !important;
    border: none !important;
}

/* Override the default bootstrap behavior where horizontal description lists 
   will truncate terms that are too long to fit in the left column 
*/
.dl-horizontal dt {
    white-space: normal;
}

/* Set widths on the form inputs since otherwise they're 100% wide */
/*input[type="text"],
input[type="password"],
input[type="email"],
input[type="tel"],
input[type="select"] {
    max-width: 280px;
}*/

/* Responsive: Portrait tablets and up */
@media screen and (min-width: 768px) {
    .jumbotron {
        margin-top: 20px;
    }

    .body-content {
        padding: 0;
    }
}


/*Για το schedule ==============================*/
/*Σε κινητό να μη φαίνεται το AddEvent*/
.e-schedule #addEvent {
    display: none;
}

/*Για τα Select2 controls =======================================*/
span.select2-selection{
    border-radius: 0px !important;
}

/*Για τα buttons στο select2*/
.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    color: black;
}

.select2-container--default .select2-selection--single .select2-selection__rendered
{
    line-height: inherit !important;
}




/*Για το Speech-to-Text*/
.input-container {
    position: relative;
    width: 400px;
    margin-top: 30px;
}

.mic-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    font-size: 20px;
    color: #666;
    transition: color 0.3s;
}

    .mic-icon:hover {
        color: #007bff;
    }

    .mic-icon.listening {
        color: #dc3545;
        animation: pulse 1.5s infinite;
    }

@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}
