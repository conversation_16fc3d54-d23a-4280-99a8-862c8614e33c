﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="appointmentCategoryIdLbl.Text" xml:space="preserve">
    <value>Κατηγορία</value>
  </data>
  <data name="AppointmentConflictsWithOthers" xml:space="preserve">
    <value>Η συνεδρία δεν μπορεί να καταχωρηθεί γιατί συμπίπτει με άλλο συμβάν.</value>
  </data>
  <data name="AppointmentIsChargeable" xml:space="preserve">
    <value>Η συνεδρία είναι χρεώσιμη</value>
  </data>
  <data name="AppointmentIsNoChargeable" xml:space="preserve">
    <value>Η συνεδρία δεν είναι χρεώσιμη</value>
  </data>
  <data name="appointmentReportLbl.Text" xml:space="preserve">
    <value>Συνεδρία</value>
  </data>
  <data name="balanceLbl.Text" xml:space="preserve">
    <value>Υπόλοιπο</value>
  </data>
  <data name="bankLbl.Text" xml:space="preserve">
    <value>Τράπεζα</value>
  </data>
  <data name="cancelAppointmentDialog.Title" xml:space="preserve">
    <value>Ακύρωση Συνεδρίας</value>
  </data>
  <data name="cancelAppointmentInfoLbl.Text" xml:space="preserve">
    <value>Επιλέξτε αν η ακυρωμένη συνεδρία είναι χρεώσιμη.</value>
  </data>
  <data name="canceledAppointmentsReportHeaderLbl" xml:space="preserve">
    <value>Ακυρωμένα Ραντεβού</value>
  </data>
  <data name="cancelUpcomingAppointmentsLbl.Text" xml:space="preserve">
    <value>Ακύρωση επόμενων συνεδριών</value>
  </data>
  <data name="ChangeToChargeable" xml:space="preserve">
    <value>Μεταβολή σε χρεώσιμη</value>
  </data>
  <data name="ChangeToNoChargeable" xml:space="preserve">
    <value>Μεταβολή σε μη χρεώσιμη</value>
  </data>
  <data name="chargeableCanceledAppointmentChkBox.Text" xml:space="preserve">
    <value>Χρεώσιμη</value>
  </data>
  <data name="ChargeableOptionRequiredMessage" xml:space="preserve">
    <value>Επιλέξτε αν η συνεδρία είναι χρεώσιμη.</value>
  </data>
  <data name="contactIdLbl.Text" xml:space="preserve">
    <value>Πελάτης</value>
  </data>
  <data name="ContactRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Πελάτης δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="deductionsLbl.Text" xml:space="preserve">
    <value>Κρατήσεις</value>
  </data>
  <data name="endTimeLbl.Text" xml:space="preserve">
    <value>Λήξη</value>
  </data>
  <data name="EndTimeRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Λήξη δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="ExceedingAppointmentRecurrenceMessage" xml:space="preserve">
    <value>Η συνεδρία επαναλαμβάνεται για χρονικό διάστημα μεγαλύτερο του ενός έτους. Μειώστε τις επαναλήψεις σε διάρκεια μικρότερη του ενός έτους και μέχρι 15 επαναλήψεις.</value>
  </data>
  <data name="intervetionModelLbl.Text" xml:space="preserve">
    <value>Μοντέλο Παρέμβασης</value>
  </data>
  <data name="intervetionTechniquesLbl.Text" xml:space="preserve">
    <value>Τεχνικές Παρέμβασης</value>
  </data>
  <data name="InvalidAppointmentRecurrenceMessage" xml:space="preserve">
    <value>Οι ρυθμίσεις για την επαναληψιμότητα δεν είναι σωστές.</value>
  </data>
  <data name="livingStatusLbl.Text" xml:space="preserve">
    <value>Καθεστώς Διαβίωσης</value>
  </data>
  <data name="noChargeableCanceledAppointmentChkBox.Text" xml:space="preserve">
    <value>Μη Χρεώσιμη</value>
  </data>
  <data name="notesLbl.Text" xml:space="preserve">
    <value>Σημειώσεις</value>
  </data>
  <data name="onlineIntervationsLbl.Text" xml:space="preserve">
    <value>Παρεμβάσεις Online</value>
  </data>
  <data name="Page.Title" xml:space="preserve">
    <value>Συνεδρία</value>
  </data>
  <data name="paymentTypeLbl.Text" xml:space="preserve">
    <value>Τρόπος Πληρωμής</value>
  </data>
  <data name="priceLbl.Text" xml:space="preserve">
    <value>Κόστος</value>
  </data>
  <data name="RecurrenceInfoMessage" xml:space="preserve">
    <value>Η συνεδρία είναι επαναλαμβανόμενη. Οι επαναλήψεις συμβαίνουν τις ημερομηνίες: {0}</value>
  </data>
  <data name="recurrentAppointmentWarningMessageLbl.Text" xml:space="preserve">
    <value>Η συνεδρία δεν πρέπει να επαναλαμβάνεται για περισσότερο από ένα έτος ή να έχει περισσότερες από 50 επαναλήψεις.</value>
  </data>
  <data name="requestLbl.Text" xml:space="preserve">
    <value>Αίτημα</value>
  </data>
  <data name="RoomAlreadyOccupiedMessage" xml:space="preserve">
    <value>Ο τόπος συνεδρίας είναι ήδη κατειλλημένος. Επιλέξτε κάποιον άλλο εκτός από {0}.</value>
  </data>
  <data name="roomLbl.Text" xml:space="preserve">
    <value>Τόπος Συνεδρίας</value>
  </data>
  <data name="SaveRecurrentAppointmentConfirmationMessage" xml:space="preserve">
    <value>Είστε σίγουρος ότι θέλετε να αποθηκεύσετε την επαναλαμβανόμενη συνεδρία;&lt;br/&gt;&lt;br/&gt;
Οι επαναλήψεις που θα δημιουργηθούν είναι:&lt;br/&gt;
{0}</value>
  </data>
  <data name="SelectAppointmentCategoryBeforeAddingTasksMessage" xml:space="preserve">
    <value>Επιλέξτε πρώτα μια κατηγορία.</value>
  </data>
  <data name="sendServiceEmailToAdmin.Text" xml:space="preserve">
    <value>Στο διαχειριστή</value>
  </data>
  <data name="sendServiceEmailToContact.Text" xml:space="preserve">
    <value>Στον πελάτη</value>
  </data>
  <data name="sendServiceEmailToUser.Text" xml:space="preserve">
    <value>Στο χρήστη</value>
  </data>
  <data name="setAppointmentNoCanceledLnk.Text" xml:space="preserve">
    <value>Αναίρεση ακύρωσης</value>
  </data>
  <data name="showContactLbl.Text" xml:space="preserve">
    <value>Εμφάνιση Πελάτη</value>
  </data>
  <data name="startDateLbl.Text" xml:space="preserve">
    <value>Ημερομηνία</value>
  </data>
  <data name="StartTimeBeforeEightMessage" xml:space="preserve">
    <value>Η ώρα της έναρξης συμβαίνει πριν τις 8:00 και το ραντεβού δεν θα είναι φανερό στο ημερολόγιο.</value>
  </data>
  <data name="StartTimeLaterThanEndTimeMessage" xml:space="preserve">
    <value>Το πεδίο Άφιξη έχει ημερομηνία μεταγενέστερη από την ημερομηνία στο πεδίο Αναχώρηση.</value>
  </data>
  <data name="startTimeLbl.Text" xml:space="preserve">
    <value>Έναρξη</value>
  </data>
  <data name="StartTimeRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Έναρξη δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="stateLbl.Text" xml:space="preserve">
    <value>Κατάσταση</value>
  </data>
  <data name="supervisionRequestLbl.Text" xml:space="preserve">
    <value>Αίτημα για Εποπτεία</value>
  </data>
  <data name="supervisorCommentsAfterLbl.Text" xml:space="preserve">
    <value>Σχόλια Επόπτη (μετά)</value>
  </data>
  <data name="supervisorInstructionsBeforeLbl.Text" xml:space="preserve">
    <value>Οδηγίες Επόπτη (πριν)</value>
  </data>
  <data name="therapistCommentsInfoLbl.Text" xml:space="preserve">
    <value>(Συμπληρώνεται από τον θεραπευτή)</value>
  </data>
  <data name="therapistCommentsLbl.Text" xml:space="preserve">
    <value>Γενικά Σχόλια</value>
  </data>
  <data name="therapistIdLbl.Text" xml:space="preserve">
    <value>Θεραπευτής</value>
  </data>
  <data name="TherapistRequiredMessage" xml:space="preserve">
    <value>Το πεδίο Θεραπευτής δεν έχει συμπληρωθεί.</value>
  </data>
  <data name="UpdateUpcomingRecurrentAppointmentsConfirmationMessage" xml:space="preserve">
    <value>Έχετε τροποποιήσει μια επαναλαμβανόμενη συνεδρία. Θέλετε οι αλλαγές να γίνουν και στις επόμενες επαναλαμβανόμενες συνεδρίες;</value>
    <comment>Έχετε αλλάξει τις ημερομηνίες/ώρες έναρξης και λήξης της επαναλαμβανόμενης συνεδρίας. Θέλετε οι αλλαγές στην ώρα να γίνουν και στις επόμενες επαναλαμβανόμενες συνεδρίες;</comment>
  </data>
</root>