﻿<%@ Page Language="C#" MasterPageFile="~/Main.master" AutoEventWireup="true" CodeBehind="Default.aspx.cs" Inherits="WebUI.Default" meta:resourcekey="Page" Async="true"  %>

<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <br />
    <div class="row">
        <div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-aqua"><i class="fa fa-users"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text"><asp:Literal ID="contactsTitleLbl" runat="server" meta:resourceKey="contactsTitleLbl"></asp:Literal></span>
                    <span class="info-box-number"><asp:Literal ID="contactsCountLbl" runat="server"></asp:Literal></span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <!-- /.col -->
        <div runat="server" id="calendarDiv" class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-green"><i class="fa fa-calendar"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text"><asp:Literal ID="futureTasksTitleLbl" runat="server" meta:resourceKey="futureTasksTitleLbl"></asp:Literal></span>
                    <span class="info-box-number"><asp:Literal ID="futureTasksCountLbl" runat="server"></asp:Literal></span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>
        <%--<asp:Button ID="Button2" OnClick="Button2_Click" runat="server" Text="Button" />--%>
        <!-- /.col -->
        <%--<div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-yellow"><i class="fa fa-files-o"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">Uploads</span>
                    <span class="info-box-number">13,648</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>--%>
        <!-- /.col -->
        <%--<div class="col-md-3 col-sm-6 col-xs-12">
            <div class="info-box">
                <span class="info-box-icon bg-red"><i class="fa fa-star-o"></i></span>

                <div class="info-box-content">
                    <span class="info-box-text">Likes</span>
                    <span class="info-box-number">93,139</span>
                </div>
                <!-- /.info-box-content -->
            </div>
            <!-- /.info-box -->
        </div>--%>
        <!-- /.col -->
    </div>
</asp:Content>
