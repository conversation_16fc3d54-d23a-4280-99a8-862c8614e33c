﻿@page "/reports"

@using Admin.Business
@using Admin.Reporting
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Grids
@using Admin.Data.Model;
@using Syncfusion.Blazor.Navigations
@using Syncfusion.Blazor.Popups
@using Syncfusion.Blazor.Charts
@using System.Data

@inject SfDialogService dialogService
@inject ITenantsBusiness tenantsBusiness
@inject IReportsFactory reportsFactory
@inject IJSRuntime js


<h3>@Resources.ReportsResources.Title</h3>

<SfAccordion ExpandMode="ExpandMode.Single">
    <AccordionItems>
        <AccordionItem Header="@Resources.ReportsResources.TenantsStatisticsExcelTitle" IconCss="e-excel e-acrdn-icons" Expanded=true>
            <ContentTemplate>
                <SfButton OnClick="ExportTenantsStatisticsExcelBtnClick">@Resources.ReportsResources.Export</SfButton>
            </ContentTemplate>
        </AccordionItem>
        <AccordionItem Header="@Resources.ReportsResources.AppointmentsCountPerYearTitle" IconCss="e-excel e-acrdn-icons" Expanded=true>
            <ContentTemplate>
                <SfButton OnClick="PreviewAppointmentsCountPerYearBtnClick">@Resources.ReportsResources.Generate</SfButton>
                <br />
                @if (appointmentsCountPerYearReportVisible == true)
                {
                    <div class="control-section">
                        <SfChart Title="@Resources.ReportsResources.AppointmentsCountPerYearTitle">
                            <FontSettings FontFamily=""></FontSettings>
                            <ChartPrimaryXAxis ValueType="Syncfusion.Blazor.Charts.ValueType.Category" Title="Έτος" />
                            <ChartPrimaryYAxis Title="Αριθμός Συνεδριών" />
                            <ChartSeriesCollection>
                                <ChartSeries DataSource="@appointmentsData" XName="Year" YName="CurrentYearAppointments"
                                             Name="Συνεδρίες Έτους" Type="ChartSeriesType.Column">
                                    <ChartMarker Visible="true" />
                                </ChartSeries>
                                <ChartSeries DataSource="@appointmentsData" XName="Year" YName="CumulativeAppointments"
                                             Name="Συνεδρίες Έτους + Προηγούμενων Ετών" Type="ChartSeriesType.Line">
                                    <ChartMarker Visible="true" />
                                </ChartSeries>
                            </ChartSeriesCollection>
                            @* <ChartTooltip Enable="true" />
                        <ChartLegend Visible="true" /> *@
                        </SfChart>
                    </div>
                }
            </ContentTemplate>
        </AccordionItem>
    </AccordionItems>
</SfAccordion>

