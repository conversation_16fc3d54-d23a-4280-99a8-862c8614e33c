﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" AutoEventWireup="true" CodeBehind="User.aspx.cs" Inherits="WebUI.User" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>


<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script type="text/javascript">
        function Initialization() {
            //$("#passwordTxtBox").Attributes["type"] = "password";


            //Δημιουργεί το DropDown για το RoleId
            $('#roleIdCmbBox').select2({
                //Your parameters
                language: {

                },
                escapeMarkup: function (markup) {
                    return markup;
                }
            });

            $("#isDoctorChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $('#isDoctorChkBox').on('switchChange.bootstrapSwitch', function (event, state) {
                if (state == true) {
                    document.getElementById('doctorDataDiv').style.display = "block";
                }
                else {
                    document.getElementById('doctorDataDiv').style.display = "none";
                }
            });
        }
    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <div class="row margin-bottom">
        <div class="col-xs-12">
            <div class="btn-group">
                <asp:LinkButton ID="saveCloseBtn" runat="server" CssClass="btn btn-primary btn-flat margin-bottom margin-r-5" OnClick="saveCloseBtn_Click"><span class="hidden-md hidden-lg"><i class="fa fa-save"></i></span><asp:Label Text="<%$ Resources:GlobalResources, SaveClose %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
                <%-- <button type="button" class="btn btn-primary btn-flat dropdown-toggle" data-toggle="dropdown">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:Button ID="saveBtn" runat="server" Text="<%$ Resources:GlobalResources, Save %>" OnClick="saveBtn_Click"></asp:Button>
                    </li>
                </ul>--%>
            </div>
            <asp:LinkButton ID="deleteBtn" runat="server" DisableValidation="True" CssClass="btn btn-danger btn-flat margin-bottom margin-r-5" OnClick="deleteBtn_Click" CausesValidation="False"><span class="hidden-md hidden-lg"><i class="fa fa-trash"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Delete %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
            <asp:LinkButton ID="cancelBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" OnClick="cancelBtn_Click" CausesValidation="False" PostBackUrl="~/Users.aspx"><span class="hidden-md hidden-lg"><i class="fa fa-remove"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Cancel %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6">
            <div class="form-horizontal">
                <%--<div class="form-group">
                    <asp:Label for="userIdTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="userIdLbl"></asp:Label>
                    <div class="col-sm-10">
                        <input type="text" runat="server" class="form-control" id="userIdTxtBox" readonly="readonly" placeholder="">
                    </div>
                </div>--%>
                <div class="form-group">
                    <asp:Label for="fullNameTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="fullNameLbl"></asp:Label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input type="text" runat="server" class="form-control " id="fullNameTxtBox" maxlength="30" placeholder="" required="required">
                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="col-sm-6">
            <div class="form-horizontal">
                <div class="form-group">
                    <asp:Label for="roleCmbBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="roleLbl"></asp:Label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <select runat="server" style="width: 100%;" datavaluefield="RoleId" datatextfield="Name" id="roleCmbBox" class="form-control"></select>
                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6">
            <div class="form-horizontal">
                <div class="form-group">
                    <asp:Label for="usernameTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="usernameLbl"></asp:Label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input type="text" runat="server" class="form-control" id="usernameTxtBox" maxlength="20" placeholder="">
                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="col-sm-6">
            <div runat="server" class="form-horizontal" id="passwordDiv">
                <div class="form-group">
                    <asp:Label for="passwordTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="passwordLbl"></asp:Label>
                    <div class="col-sm-8">
                        <div class="input-group">
                            <input type="password" runat="server" class="form-control" id="passwordTxtBox" maxlength="20" placeholder="">
                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6">
            <div class="form-horizontal">
                <div class="form-group">
                    <asp:Label for="emailTxtBox" runat="server" class="col-sm-4 control-label" meta:resourcekey="emailLbl"></asp:Label>
                    <div class="col-sm-8">
                        <input type="text" runat="server" class="form-control " id="emailTxtBox" maxlength="50" placeholder="">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-6">
            <div id="isDoctorDiv" class="form-horizontal label-left">
                <div class="form-group">
                    <asp:Label runat="server" for="isDoctorChkBox" CssClass="col-sm-4 control-label" ID="isDoctorLbl" meta:resourcekey="isDoctorLbl"></asp:Label>
                    <div class="col-sm-8 switch">
                        <input runat="server" type="checkbox" id="isDoctorChkBox">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="doctorDataDiv" runat="server" class="row">
        <div class="col-sm-12">
            <!-- general form elements -->
            <div class="box">
                <div class="box-header with-border">
                    <h3 class="box-title">
                        <asp:Label runat="server" meta:resourcekey="doctorDataTitleLbl"></asp:Label>
                    </h3>
                </div>
                <!-- /.box-header -->

                <div class="box-body">
                    <div class="form-horizontal label-left">
                        <div class="form-group">
                            <asp:Label for="jobTitleTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="jobTitleLbl"></asp:Label>
                            <div class="col-sm-10">
                                <input type="text" runat="server" class="form-control " id="jobTitleTxtBox" maxlength="50" placeholder="">
                            </div>
                        </div>
                        <div class="form-group">
                            <asp:Label for="academicTitleTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="academicTitleLbl"></asp:Label>
                            <div class="col-sm-10">
                                <input type="text" runat="server" class="form-control " id="academicTitleTxtBox" maxlength="50" placeholder="">
                            </div>
                        </div>
                        <div class="form-group">
                            <asp:Label for="specialisationTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="specialisationLbl"></asp:Label>
                            <div class="col-sm-10">
                                <input type="text" runat="server" class="form-control " id="specialisationTxtBox" maxlength="50" placeholder="">
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /.box-body -->

            </div>
            <!-- /.box -->
        </div>
    </div>
</asp:Content>
