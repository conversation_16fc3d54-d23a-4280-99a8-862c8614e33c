﻿using Data;
using Newtonsoft.Json;
//using Syncfusion.JavaScript.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace WebUI
{
    public partial class User : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = null;

                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                Int64 loggedUserId = Convert.ToInt64(userData["UserId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    Response.StatusCode = 404;
                    Response.End();
                }



                if (!IsPostBack)
                {
                    if (this.roleCmbBox.Items.Count == 0)
                    {
                        Data.MentalViewDataSet rolesDS = Data.Business.AdministrationBusiness.GetAllRoles(tenantId);
                        this.roleCmbBox.Items.Clear();
                        foreach (MentalViewDataSet.RolesRow taskCategoriesRow in rolesDS.Roles)
                        {
                            this.roleCmbBox.Items.Add(new ListItem(taskCategoriesRow.Name, taskCategoriesRow.RoleId.ToString()));
                        }
                    }

                    //Αν δημιουργούμε νέο User
                    if (string.IsNullOrEmpty(this.Request.Params["UserId"]))
                    {
                        //Αφαιρεί την επιλογή του SuperAdmin γιατί δε θέλουμε να δημιουργηθεί 2ο SuperAdmin, είτε με νέο User.
                        this.roleCmbBox.Items.Remove(this.roleCmbBox.Items.FindByText("SuperAdmin"));

                        ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;
                        Data.Business.ConfigureDataSet(ref ds);

                        //Δημιουργεί το νεο User
                        MentalViewDataSet.UsersRow usersRow = ds.Users.NewUsersRow();

                        usersRow.TenantId = tenantId;
                        usersRow.RoleId = 1;
                        usersRow.IsDoctor = false;
                        ds.Users.AddUsersRow(usersRow);

                        ViewState["User"] = ds;

                    }
                    else  //Αν κάνουμε επεξεργασία
                    {
                        int userId = int.Parse(this.Request.Params["UserId"]);

                        ds = (Data.MentalViewDataSet)Data.Business.UsersBusiness.GetUserById(userId);

                        ViewState["User"] = ds;

                        //Αν ο User είναι Super Admin τότε εμποδίζουμε να αλλάξει το Rolo και το password (το password αλλάζει από το Joomla)
                        if (ds.Users[0].RolesRow.Name == "SuperAdmin")
                        {
                            this.roleCmbBox.Disabled = true;  //Ο SuperAdmin δεν μπορεί να αλλάξει το Role του.
                            this.passwordDiv.Visible = false;
                            this.deleteBtn.Visible = false;
                        }
                        else  //Αν δεν κάνουμε επεξεργασία του SuperAdmin
                        {
                            //Αφαιρεί την επιλογή του SuperAdmin γιατί δε θέλουμε να δημιουργηθεί 2ο SuperAdmin, με επεξεργασία ενός User.
                            this.roleCmbBox.Items.Remove(this.roleCmbBox.Items.FindByText("SuperAdmin"));
                        }
                    }

                    //Βρίσκει τα στοιχεία του User για το user
                    //MentalViewDataSet userDS = Data.Business.AdministrationBusiness.GetUserByUserId(ds.Users[0].UserId);
                    //ViewState["UserUserFullName"] = userDS.Users[0].FullName;

                    this.SetDataOnUIControls(ds);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void SetDataOnUIControls(Data.MentalViewDataSet ds)
        {
            this.fullNameTxtBox.Value = ds.Users[0].FullName;
            this.usernameTxtBox.Value = ds.Users[0].Username;
            this.passwordTxtBox.Value = ds.Users[0].Password;
            this.emailTxtBox.Value = ds.Users[0].Email;
            this.roleCmbBox.Value = ds.Users[0].RoleId.ToString();
            this.isDoctorChkBox.Checked = ds.Users[0].IsDoctor;
            this.jobTitleTxtBox.Value = ds.Users[0].JobTitle;
            this.academicTitleTxtBox.Value = ds.Users[0].AcademicTitle;
            this.specialisationTxtBox.Value = ds.Users[0].Specialisation;
            this.doctorDataDiv.Style["display"] = ds.Users[0].IsDoctor ? "block" : "none";
        }

        private void GetDataFromUIControls(ref MentalViewDataSet ds)
        {
            try
            {
                ds.Users[0].FullName = this.fullNameTxtBox.Value;
                ds.Users[0].Username = this.usernameTxtBox.Value;
                ds.Users[0].Email = this.emailTxtBox.Value;
                if (this.passwordTxtBox.Value.Trim() != "")
                {
                    ds.Users[0].Password = this.passwordTxtBox.Value;
                }
                ds.Users[0].RoleId = Int64.Parse(this.roleCmbBox.Value);
                ds.Users[0].IsDoctor = this.isDoctorChkBox.Checked;
                ds.Users[0].JobTitle = this.jobTitleTxtBox.Value;
                ds.Users[0].AcademicTitle = this.academicTitleTxtBox.Value;
                ds.Users[0].Specialisation = this.specialisationTxtBox.Value;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveCloseBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Save(bool close)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["User"];
                ds.EnforceConstraints = false;

                this.GetDataFromUIControls(ref ds);

                if (this.ValidateData(ds.Users[0]) == true)
                {
                    Data.Business.UsersBusiness.SaveUsers(ref ds);
                    ViewState["User"] = ds;

                    if (close)
                    {
                        ViewState.Remove("User");
                        Response.Redirect(@"~\Users.aspx");
                    }
                    else
                    {
                        this.SetDataOnUIControls(ds);
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveBtn_Click(object sender, EventArgs e)
        {

        }

        protected void cancelBtn_Click(object sender, EventArgs e)
        {
            ViewState.Remove("User");
            Response.Redirect(@"Users.aspx");
        }

        protected void deleteBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["User"];

                //if (ds.Users[0].RowState != System.Data.DataRowState.Added)
                //{
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete");
                //}
                //else
                //{
                //    ViewState.Remove("User");
                //    Response.Redirect(@"Users.aspx");
                //}

                this.GetDataFromUIControls(ref ds);
                ViewState["User"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Delete()
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["User"];

                if (Data.Business.AppointmentsBusiness.GetTotalAppointmentByUserId(ds.Users[0].UserId) > 0)
                {
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("CannotDeleteUserWithAppointmentsMessage").ToString(), ServerMessageButtons.Ok);       
                }
                else
                {
                    ds.Users[0].Delete();
                    Data.Business.SaveAllData(ds);

                    ViewState.Remove("User");
                    Response.Redirect(@"Users.aspx");
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        this.Delete();
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["User"];
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateData(Data.MentalViewDataSet.UsersRow usersRow)
        {
            try
            {
                //Αν το πεδίο FullName δεν είναι συμπληρωμένο
                if (usersRow.FullName == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("FullNameRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν το πεδίο Username δεν είναι συμπληρωμένο
                if (usersRow.Username == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("UsernameRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                if (Data.Business.UsersBusiness.CheckUsernameExists(usersRow.UserId, usersRow.Username) == true)
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("UsernameExistsMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                return true;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }
    }
}