﻿using C1.C1Report;
using System;
using System.Globalization;
using System.IO;
using System.Net;
using System.Net.Mail;
using System.Text.RegularExpressions;
using System.Web.Configuration;
using System.Web;
using System.Web.Hosting;
using System.Net.Mime;
using System.Data;
using System.Web.Services.Description;
using Data;
using static Data.MentalViewDataSet;
using System.Reflection;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace WebUI
{
    public class EmailManager
    {
        //bool invalidEmail = false;
        private string htmlSignature = @"<br/>
        <span style=""font-size:16px""><a href=""http://mentalview.gr"">MentalView</a></span>";
        private string host = string.Empty;
        private int port = 0;
        private string emailAcountUserName = string.Empty;
        private string emailAcountPassword = string.Empty;
        private string senderEmail = string.Empty;
        private string senderName = string.Empty;
        private bool useDefaultCredentials = true;
        private bool enableSsl = false;

        //public string Host
        //{
        //    get { return host; }
        //    set { host = value; }
        //}

        //public string Port
        //{
        //    get { return port; }
        //    set { port = value; }
        //}

        //public string UserName
        //{
        //    get { return userName; }
        //    set { userName = value; }
        //}

        //public string Password
        //{
        //    get { return password; }
        //    set { password = value; }
        //}

        //public bool UseDefaultCredentials
        //{
        //    get { return useDefaultCredentials; }
        //    set { useDefaultCredentials = value; }
        //}

        //public bool EnableSsl
        //{
        //    get { return enableSsl; }
        //    set { enableSsl = value; }
        //}

        //public string HtmlSignature
        //{
        //    get { return htmlSignature; }
        //    set { htmlSignature = value; }
        //}

        public EmailManager(string host, int port, string emailAcountUserName, string emailAcountPassword, bool useDefaultCredentials, bool enableSsl, string senderEmail, string senderName, string htmlSignature)
        {
            this.host = host;
            this.port = port;
            this.emailAcountUserName = emailAcountUserName;
            this.emailAcountPassword = emailAcountPassword;
            this.useDefaultCredentials = useDefaultCredentials;
            this.senderEmail = senderEmail;
            this.senderName = senderName;
            this.enableSsl = enableSsl;
            this.htmlSignature = htmlSignature;
        }

        public static bool IsValidEmail(string strIn)
        {
            bool invalidEmail = false;
            if (String.IsNullOrEmpty(strIn))
                return false;

            // Use IdnMapping class to convert Unicode domain names. 
            try
            {
                strIn = Regex.Replace(strIn, @"(@)(.+)$", DomainMapper, RegexOptions.None, TimeSpan.FromMilliseconds(200));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }

            if (invalidEmail)
                return false;

            // Return true if strIn is in valid e-mail format. 
            try
            {
                return Regex.IsMatch(strIn,
                      @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                      @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-\w]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                      RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        }

        private static string DomainMapper(Match match)
        {
            // IdnMapping class with default property values.
            IdnMapping idn = new IdnMapping();

            string domainName = match.Groups[2].Value;
            try
            {
                domainName = idn.GetAscii(domainName);
            }
            catch (ArgumentException)
            {
                //invalidEmail = true;
                return "invalid email";
            }
            return match.Groups[1].Value + domainName;
        }

        /// <summary>
        /// Στέλνει email με το invoice.
        /// </summary>



        public void SendSUpdateAppointmentNotificationToTherapist(MentalViewDataSet currentValuesDS, MentalViewDataSet originalValuesDS, bool isNewAppointment)
        {
            try
            {
                currentValuesDS.Merge(Data.Business.UsersBusiness.GetUserById(currentValuesDS.Appointments[0].UserId).Users);  //Διαβάζει τον Therapist
                currentValuesDS.Merge(Data.Business.ContactsBusiness.GetContactById(currentValuesDS.Appointments[0].ContactId).Contacts);
                //if (currentValuesDS.Appointments[0].IsNull(currentValuesDS.Appointments.AppointmentCategoryIdColumn) == false)
                //{ 
                //    currentValuesDS.Merge(Data.Business.AppointmentCategoriesBusiness.GetByAppointmentCategoryId(currentValuesDS.Appointments[0].AppointmentCategoryId).AppointmentCategories);
                //}
                if (originalValuesDS.Appointments.Count > 0)
                {
                    originalValuesDS.Merge(Data.Business.UsersBusiness.GetUserById(originalValuesDS.Appointments[0].UserId).Users);  //Διαβάζει τον Therapist
                }

                //if (originalValuesDS.Appointments.Count > 0)
                //{
                //    originalValuesDS.Merge(Data.Business.UsersBusiness.GetUserById(originalValuesDS.Appointments[0].UserId).Users);  //Διαβάζει τον Therapist
                //    if (originalValuesDS.Appointments[0].IsNull(originalValuesDS.Appointments.AppointmentCategoryIdColumn) == false)
                //    {
                //        originalValuesDS.Merge(Data.Business.AppointmentCategoriesBusiness.GetByAppointmentCategoryId(originalValuesDS.Appointments[0].AppointmentCategoryId).AppointmentCategories);
                //    }
                //}

                //Data.MentalViewDataSet.UsersRow currentUsersRow = currentValuesDS.Users[0];
                //Data.MentalViewDataSet.AppointmentsRow currentAppointmentsRow = currentValuesDS.Appointments[0];

                if (currentValuesDS.Users[0].Email != "")
                {
                    if (EmailManager.IsValidEmail(currentValuesDS.Users[0].Email))
                    {
                        #region  Συλλέγει τις πληροφορίες για το δωμάτιο
                        string currentRoomName = "";
                        string currentRoomBackColor = "";
                        if (currentValuesDS.Appointments[0].Room != "")
                        {
                            DataRow roomRow = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"].Select("RoomId='" + currentValuesDS.Appointments[0].Room + "'")[0];
                            currentRoomName = roomRow["RoomName"].ToString();
                            currentRoomBackColor = roomRow["BackColor"].ToString();
                        }

                        string originalRoomName = "";
                        string originalRoomBackColor = "";
                        if (originalValuesDS.Appointments.Count > 0 && originalValuesDS.Appointments[0].Room != "")
                        {
                            DataRow roomRow = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"].Select("RoomId='" + originalValuesDS.Appointments[0].Room + "'")[0];
                            originalRoomName = roomRow["RoomName"].ToString();
                            originalRoomBackColor = roomRow["BackColor"].ToString();
                        }
                        #endregion

                        string body = string.Empty;
                        string subject = string.Empty;
                        if (isNewAppointment)
                        {
                            subject = "Δημιουργία Συνεδρίας";
                        }
                        else
                        {
                            subject = "Ενημέρωση Συνεδρίας";
                        }

                        #region  Body                        
                        body += @"<span style=""font-family:Segoe Ui,sans-serif"">";
                        if (isNewAppointment)
                        {
                            body += @"Δημιουργήθηκε η συνεδρία με τα παρακάτω στοιχεία" + @".<br/><br/>";
                        }
                        else
                        {
                            body += @"Πραγματοποιήθηκε αλλαγή στην συνεδρία με τα παρακάτω στοιχεία" + @".<br/><br/>";
                        }
                        body += @"<span style=""font-size:18px; font-weight:600;"">Πελάτης</span><br/>"
                        + @"Ονοματεπώνυμο:  " + currentValuesDS.Contacts[0].FullName + "<br/>";
                        if (originalValuesDS.Contacts.Count > 0)
                        {
                            body += (originalValuesDS.Contacts[0].FullName != currentValuesDS.Contacts[0].FullName ? "<i>(αρχική τιμή: " + originalValuesDS.Contacts[0].FullName + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Τηλέφωνο:  " + currentValuesDS.Contacts[0].Phone1 + "<br/>";
                        if (originalValuesDS.Contacts.Count > 0)
                        {
                            body += (originalValuesDS.Contacts[0].Phone1 != currentValuesDS.Contacts[0].Phone1 ? "<i>(αρχική τιμή: " + originalValuesDS.Contacts[0].Phone1 + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Κινητό:  " + currentValuesDS.Contacts[0].Mobile1 + "<br/>";
                        if (originalValuesDS.Contacts.Count > 0)
                        {
                            body += (originalValuesDS.Contacts[0].Mobile1 != currentValuesDS.Contacts[0].Mobile1 ? "<i>(αρχική τιμή: " + originalValuesDS.Contacts[0].Mobile1 + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Email 1:  " + currentValuesDS.Contacts[0].Email + "<br/>";
                        if (originalValuesDS.Contacts.Count > 0)
                        {
                            body += (originalValuesDS.Contacts[0].Email != currentValuesDS.Contacts[0].Email ? "<i>(αρχική τιμή: " + originalValuesDS.Contacts[0].Email + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Email 2:  " + currentValuesDS.Contacts[0].Email2 + "<br/>";
                        if (originalValuesDS.Contacts.Count > 0)
                        {
                            body += (originalValuesDS.Contacts[0].Email2 != currentValuesDS.Contacts[0].Email2 ? "<i>(αρχική τιμή: " + originalValuesDS.Contacts[0].Email + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"<br/>"
                        + @"<span style=""font-size:18px; font-weight:600;"">Συνεδρία</span><br/>";

                        body += @"Ημερομηνία:  " + currentValuesDS.Appointments[0].StartTime.ToString("dd/MM/yyyy") + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].StartTime.ToString("dd/MM/yyyy") != currentValuesDS.Appointments[0].StartTime.ToString("dd/MM/yyyy") ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].StartTime.ToString("dd/MM/yyyy") + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Έναρξη-Λήξη:  " + currentValuesDS.Appointments[0].StartTime.ToShortTimeString() + "-" + currentValuesDS.Appointments[0].EndTime.ToShortTimeString() + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += ((originalValuesDS.Appointments[0].StartTime.ToShortTimeString() != currentValuesDS.Appointments[0].StartTime.ToShortTimeString() || originalValuesDS.Appointments[0].EndTime.ToShortTimeString() != currentValuesDS.Appointments[0].EndTime.ToShortTimeString()) ? "<i>(αρχική τιμή: " + currentValuesDS.Appointments[0].StartTime.ToShortTimeString() + "-" + currentValuesDS.Appointments[0].EndTime.ToShortTimeString() + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Θεραπευτής:  " + currentValuesDS.Appointments[0].UsersRow.FullName + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].UsersRow.FullName != currentValuesDS.Appointments[0].UsersRow.FullName ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].UsersRow.FullName + @")</i><br/><br/>" : "<br/>");
                        }

                        body += @"Τόπος Συνεδρίας:  " + currentRoomName + @" " + "<br/>";
                        body += (currentRoomName != originalRoomName ? "<i>(αρχική τιμή: " + originalRoomName + @")</i><br/><br/>" : @"<br/>");

                        //body += @"Κατηγορία:  " + ((currentValuesDS.Appointments[0].IsAppointmentCategoryIdNull() == false) ? (currentValuesDS.Appointments[0].AppointmentCategoriesRow.CategoryName + @" " + "<br/>") : "");
                        //if (originalValuesDS.Appointments.Count > 0 && originalValuesDS.Appointments[0].AppointmentCategoriesRow != null)
                        //{
                        //    body += (originalValuesDS.Appointments[0].AppointmentCategoriesRow.CategoryName != currentValuesDS.Appointments[0].AppointmentCategoriesRow.CategoryName ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].AppointmentCategoriesRow.CategoryName + @")</i><br/><br/>" : @"<br/>");
                        //}

                        //body += @"Κατάσταση:  " + Data.FieldValuesMappings.GetDisplayOfSingleValue("Appointments-AppointmentStates", currentValuesDS.Appointments[0].State) + "<br/>";
                        //if (originalValuesDS.Appointments.Count > 0)
                        //{
                        //    body += (originalValuesDS.Appointments[0].State != currentValuesDS.Appointments[0].State ? "<i>(αρχική τιμή: " + Data.FieldValuesMappings.GetDisplayOfSingleValue("Appointments-AppointmentStates", originalValuesDS.Appointments[0].State) + @")</i><br/><br/>" : @"<br/>");
                        //}

                        body += @"Αίτημα:  " + currentValuesDS.Appointments[0].Request + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].Request != currentValuesDS.Appointments[0].Request ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].Request + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Οδηγίες Επόπτη (πριν):  " + currentValuesDS.Appointments[0].SupervisorInstructionsBefore + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].SupervisorInstructionsBefore != currentValuesDS.Appointments[0].SupervisorInstructionsBefore ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].SupervisorInstructionsBefore + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Μοντέλο Παρέμβασης:  " + Data.FieldValuesMappings.GetDisplayOfMultipleValues("Appointments-IntervetionModels", currentValuesDS.Appointments[0].IntervetionModel) + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            string originalValue = FieldValuesMappings.GetDisplayOfMultipleValues("Appointments-IntervetionModels", originalValuesDS.Appointments[0].IntervetionModel);
                            body += (originalValuesDS.Appointments[0].IntervetionModel != currentValuesDS.Appointments[0].IntervetionModel ? "<i>(αρχική τιμή: " + originalValue + @")</i><br/><br/>" : @"<br/>"); //Data.FieldValuesMappings.GetDisplayOfSingleValue("Appointments-IntervetionModels", originalValuesDS.Appointments[0].IntervetionModel) + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Τεχνικές Παρέμβασης:  " + Data.FieldValuesMappings.GetDisplayOfMultipleValues("Appointments-IntervetionTechniques", currentValuesDS.Appointments[0].IntervetionTechniques) + "<br/>";//+ currentValuesDS.Appointments[0].IntervetionTechniques + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            string originalValue = FieldValuesMappings.GetDisplayOfMultipleValues("Appointments-IntervetionTechniques", originalValuesDS.Appointments[0].IntervetionTechniques);
                            body += (originalValuesDS.Appointments[0].IntervetionTechniques != currentValuesDS.Appointments[0].IntervetionTechniques ? "<i>(αρχική τιμή: " + originalValue + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Γενικά Σχόλια:  " + currentValuesDS.Appointments[0].TherapistComments + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].TherapistComments != currentValuesDS.Appointments[0].TherapistComments ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].TherapistComments + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Αίτημα για Εποπτία:  " + (currentValuesDS.Appointments[0].SupervisionRequest ? "Ναι" : "Όχι") + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].SupervisionRequest != currentValuesDS.Appointments[0].SupervisionRequest ? "<i>(αρχική τιμή: " + (originalValuesDS.Appointments[0].SupervisionRequest ? "Ναι" : "Όχι") + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Σχόλια Επόπτη (μετά):  " + currentValuesDS.Appointments[0].SupervisorCommentsAfter + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].SupervisorCommentsAfter != currentValuesDS.Appointments[0].SupervisorCommentsAfter ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].SupervisorCommentsAfter + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Κόστος:  " + currentValuesDS.Appointments[0].Price.ToString("C") + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].Price != currentValuesDS.Appointments[0].Price ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].Price.ToString("C") + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Κρατήσεις:  " + currentValuesDS.Appointments[0].Deductions.ToString("C") + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].Deductions != currentValuesDS.Appointments[0].Deductions ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].Deductions.ToString("C") + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Τρόπος Πληρωμής:  " + currentValuesDS.Appointments[0].PaymentType + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].PaymentType != currentValuesDS.Appointments[0].PaymentType ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].PaymentType + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Τράπεζα:  " + currentValuesDS.Appointments[0].Bank + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].Bank != currentValuesDS.Appointments[0].Bank ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].Bank + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"Σημειώσεις:  " + currentValuesDS.Appointments[0].Notes + @" " + "<br/>";
                        if (originalValuesDS.Appointments.Count > 0)
                        {
                            body += (originalValuesDS.Appointments[0].Notes != currentValuesDS.Appointments[0].Notes ? "<i>(αρχική τιμή: " + originalValuesDS.Appointments[0].Notes + @")</i><br/><br/>" : @"<br/>");
                        }

                        body += @"<br/>"
                        + htmlSignature
                        + @"</span>";

                        #endregion

                        //SendEmailFromInfo(new string[] { currentValuesDS.Users[0].Email }, subject, body, true, null, true);
                        SendEmail(new string[] { currentValuesDS.Users[0].Email }, subject, body, true, null, true);
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        /// <summary>
        /// Ενημερώνει όσους εμπλέκονται σε Συμβάν για εποπτεία. Το function είναι μόνο για Συμβάν.
        /// </summary>
        /// <param name="therapistsIDs"></param>
        /// <param name="appointmentDS"></param>
        public void SendTaskSupervisionNotificationToTherapists(string[] therapistsIDs, MentalViewDataSet appointmentDS)
        {
            try
            {
                List<string> recipients = new List<string>();
                appointmentDS.Merge(Data.Business.UsersBusiness.GetUserById(appointmentDS.Appointments[0].UserId).Users);  //Διαβάζει τον Therapist
                                                                                                                           //appointmentDS.Merge(Data.Business.ContactsBusiness.GetContactById(appointmentDS.Appointments[0].ContactId).Contacts);

                //if (appointmentDS.Appointments[0].IsNull(appointmentDS.Appointments.AppointmentCategoryIdColumn) == false)
                //{
                //    appointmentDS.Merge(Data.Business.AppointmentCategoriesBusiness.GetByAppointmentCategoryId(appointmentDS.Appointments[0].AppointmentCategoryId).AppointmentCategories);
                //}

                foreach (string therapistId in therapistsIDs)
                {
                    MentalViewDataSet therapistDS = Data.Business.UsersBusiness.GetUserById(Convert.ToInt64(therapistId));  //Διαβάζει τον θεραπευτή.

                    if (string.IsNullOrEmpty(therapistDS.Users[0].Email) == false)
                    {
                        recipients.Add(therapistDS.Users[0].Email);
                    }
                }

                string body = "";
                string subject = "Συμβάν";
                if (appointmentDS.Appointments[0].TaskSupervision)
                {
                    subject += " με Εποπτεία";
                }

                #region  Body                        
                body += @"<span style=""font-family:Segoe Ui,sans-serif"">";
                //+ @"Πραγματοποιήθηκε αλλαγή στην εποπτεία με τα παρακάτω στοιχεία" + @".<br/><br/>"
                //+ @"<span style=""font-size:18px; font-weight:600;"">Πελάτης</span><br/>"
                //+ @"Ονοματεπώνυμο:  " + appointmentDS.Contacts[0].FullName + "<br/>";
                //if (appointmentDS.Contacts.Count > 0)
                //{
                //    body += appointmentDS.Contacts[0].FullName;
                //}

                //body += @"Τηλέφωνο:  " + appointmentDS.Contacts[0].Phone1 + "<br/>";
                //if (appointmentDS.Contacts.Count > 0)
                //{
                //    body += appointmentDS.Contacts[0].Phone1;
                //}

                body += @"<br/>"
               + @"<span style=""font-size:18px; font-weight:600;"">Συμβάν</span><br/>";

                body += @"Ημερομηνία:  " + appointmentDS.Appointments[0].StartTime.ToString("dd/MM/yyyy") + "<br/>";
                body += @"Έναρξη-Λήξη:  " + appointmentDS.Appointments[0].StartTime.ToShortTimeString() + "-" + appointmentDS.Appointments[0].EndTime.ToShortTimeString() + "<br/>";
                body += @"Περιγραφή:  " + appointmentDS.Appointments[0].Subject + "<br/>";
                body += @"Θεραπευτής:  " + appointmentDS.Appointments[0].UsersRow.FullName + "<br/>";

                if (appointmentDS.Appointments[0].TaskSupervision)
                {
                    body += @"<br/>"
                    + @"<span style=""font-size:18px; font-weight:600;"">Εποπτεία</span><br/>";

                    body += @"Πελάτες:  " + appointmentDS.Appointments[0].TaskSupervisionCustomers + "<br/>";
                    body += @"Θέμα:  " + appointmentDS.Appointments[0].TaskSupervisionSubject + "<br/>";
                    body += @"Απαντήσεις:  " + appointmentDS.Appointments[0].TaskSupervisionReplies + "<br/>";
                }
                #endregion

                if (recipients.Count > 0)
                {
                    //SendEmailFromInfo(recipients.ToArray(), subject, body, true, "");
                    SendEmail(recipients.ToArray(), subject, body, true, null, true);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }


        /// <summary>
        /// Ενημερώνει τον Παναγιωτόπουλο για Αίτημα για Εποπτεία μόνο σε Appointments.
        /// </summary>
        /// <param name="therapistsIDs"></param>
        /// <param name="appointmentDS"></param>
        public void SendAppointmentSupervisionRequestNotificationToTherapists(string[] therapistsIDs, MentalViewDataSet appointmentDS)
        {
            try
            {
                List<string> recipients = new List<string>();
                appointmentDS.Merge(Data.Business.UsersBusiness.GetUserById(appointmentDS.Appointments[0].UserId).Users);  //Διαβάζει τον Therapist
                                                                                                                           //appointmentDS.Merge(Data.Business.ContactsBusiness.GetContactById(appointmentDS.Appointments[0].ContactId).Contacts);

                //if (appointmentDS.Appointments[0].IsNull(appointmentDS.Appointments.AppointmentCategoryIdColumn) == false)
                //{
                //    appointmentDS.Merge(Data.Business.AppointmentCategoriesBusiness.GetByAppointmentCategoryId(appointmentDS.Appointments[0].AppointmentCategoryId).AppointmentCategories);
                //}

                foreach (string therapistId in therapistsIDs)
                {
                    MentalViewDataSet therapistDS = Data.Business.UsersBusiness.GetUserById(Convert.ToInt64(therapistId));  //Διαβάζει τον θεραπευτή.

                    if (string.IsNullOrEmpty(therapistDS.Users[0].Email) == false)
                    {
                        recipients.Add(therapistDS.Users[0].Email);
                    }
                }

                string body = "";
                string subject = "Νέο αίτημα για εποπτεία σε συνεδρεία";


                #region  Body                        
                body += @"<span style=""font-family:Segoe Ui,sans-serif"">";
                //+ @"Πραγματοποιήθηκε αλλαγή στην εποπτεία με τα παρακάτω στοιχεία" + @".<br/><br/>"
                //+ @"<span style=""font-size:18px; font-weight:600;"">Πελάτης</span><br/>"
                //+ @"Ονοματεπώνυμο:  " + appointmentDS.Contacts[0].FullName + "<br/>";
                //if (appointmentDS.Contacts.Count > 0)
                //{
                //    body += appointmentDS.Contacts[0].FullName;
                //}

                //body += @"Τηλέφωνο:  " + appointmentDS.Contacts[0].Phone1 + "<br/>";
                //if (appointmentDS.Contacts.Count > 0)
                //{
                //    body += appointmentDS.Contacts[0].Phone1;
                //}

                body += @"<br/>"
               + @"<span style=""font-size:18px; font-weight:600;"">Συνεδρεία</span><br/>";

                body += @"Ημερομηνία:  " + appointmentDS.Appointments[0].StartTime.ToString("dd/MM/yyyy") + "<br/>";
                body += @"Έναρξη-Λήξη:  " + appointmentDS.Appointments[0].StartTime.ToShortTimeString() + "-" + appointmentDS.Appointments[0].EndTime.ToShortTimeString() + "<br/>";
                body += @"Περιγραφή:  " + appointmentDS.Appointments[0].Subject + "<br/>";
                body += @"Θεραπευτής:  " + appointmentDS.Appointments[0].UsersRow.FullName + "<br/><br/>";
                body += @"link συνεδρείας:  <a href=""" + System.Configuration.ConfigurationManager.AppSettings["domain"] + @"appointment?AppointmentId=" + appointmentDS.Appointments[0].AppointmentId.ToString() + @""">" + System.Configuration.ConfigurationManager.AppSettings["domain"] + "appointment?AppointmentId=" + appointmentDS.Appointments[0].AppointmentId.ToString() + @" </a><br/>";

                //if (appointmentDS.Appointments[0].TaskSupervision)
                //{
                //    body += @"<br/>"
                //    + @"<span style=""font-size:18px; font-weight:600;"">Εποπτεία</span><br/>";

                //    body += @"Πελάτες:  " + appointmentDS.Appointments[0].TaskSupervisionCustomers + "<br/>";
                //    body += @"Θέμα:  " + appointmentDS.Appointments[0].TaskSupervisionSubject + "<br/>";
                //    body += @"Απαντήσεις:  " + appointmentDS.Appointments[0].TaskSupervisionReplies + "<br/>";
                //}
                #endregion

                if (recipients.Count > 0)
                {
                    //SendEmailFromInfo(recipients.ToArray(), subject, body, true, "");
                    SendEmail(recipients.ToArray(), subject, body, true, null, true);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        //private void SendEmailFromInfo(string[] recipients, string subject, string body, bool isHtmlBody, string fileName, bool replyToCom = false)
        //{
        //    SendEmail(recipients, subject, body, isHtmlBody, fileName, "<EMAIL>", "infUser@33", replyToCom);
        //}

        public void SendAppointmentReminderEmailToContact(MentalViewDataSet.UsersRow user, MentalViewDataSet.AppointmentsRow appointment, MentalViewDataSet.ContactsRow contact)
        {
            try
            {
                //Αν ο Ασθενής έχει email
                //client.Email = "<EMAIL>";
                if (contact.Email != "")
                {
                    if (EmailManager.IsValidEmail(contact.Email))
                    {
                        string body = "";
                        string subject = "Υπενθύμιση ραντεβού στις " + appointment.StartTime.ToString("dddd dd/MM/yyyy") + " " + appointment.StartTime.ToString("HH:mm");

                        #region  Body

                        body += "Υπενθυμίζουμε το ραντεβού σας με τον κ/κα " + user.FullName
                             + @", την " + appointment.StartTime.ToString("dddd dd/MM/yyyy") + " " + appointment.StartTime.ToString("HH:mm")
                             + ".<br/>Αν χρειάζεστε αλλαγή ή ακύρωση του ραντεβού σας παρακαλώ επικοινωνήστε με τον θεραπευτή σας ";  // + user.ful + ".<br/><br/>";
                        //body += @"Ευχαριστούμε πολύ.<br/>SmartPa";
                        body += "<br/><br/>";
                        body += htmlSignature;

                        #endregion

                        SendEmail(new string[] { contact.Email }, subject, body, true, "", false);
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        private void SendEmail(string[] recipients, string subject, string body, bool isHtmlBody, string fileName, bool replyToCom = false)
        {
            using (System.Net.Mail.MailMessage mailMessage = new System.Net.Mail.MailMessage())
            {
                SmtpClient smtpClient = new SmtpClient();

                try
                {
                    smtpClient = new SmtpClient();
                    smtpClient.Host = this.host;//"winzone59.grserver.gr";
                    smtpClient.Port = this.port; //587; 
                    smtpClient.UseDefaultCredentials = this.useDefaultCredentials;  //false;
                    smtpClient.EnableSsl = this.enableSsl;  //true;

                    NetworkCredential credentials = new NetworkCredential(this.emailAcountUserName, this.emailAcountPassword);
                    smtpClient.Credentials = credentials;

                    mailMessage.From = new System.Net.Mail.MailAddress(this.senderEmail, this.senderName);
                    if (replyToCom)
                    {
                        mailMessage.ReplyTo = new System.Net.Mail.MailAddress("<EMAIL>", "MentalView");
                    }

                    foreach (string recipient in recipients)
                    {
                        mailMessage.To.Add(recipient);
                    }
                    //mailMessage.To.Add("<EMAIL>");
                    mailMessage.Subject = subject;
                    mailMessage.Body = body;
                    mailMessage.IsBodyHtml = isHtmlBody;

                    //Adds the attachments
                    if (fileName != null && fileName != "")
                    {
                        Attachment attachment = new Attachment(fileName);
                        mailMessage.Attachments.Add(attachment);
                    }

                    // Send SMTP mail
                    smtpClient.Send(mailMessage);
                }
                catch (Exception exp)
                {
                    Data.ExceptionLogger.LogException(exp);
                    throw;
                }
                finally
                {
                    if (mailMessage.Attachments.Count > 0)
                    {
                        mailMessage.Attachments.Dispose();
                    }
                    //File.Delete(fileName);
                }
            }


            //using (System.Net.Mail.MailMessage mailMessage = new System.Net.Mail.MailMessage())
            //{
            //    SmtpClient smtpClient = new SmtpClient();
            //    //MailMessage message = new MailMessage();

            //    try
            //    {
            //        smtpClient = new SmtpClient();
            //        smtpClient.Host = "mail.ctparis.eu";  //"*************";  //"mail.e-omicron.gr"
            //        smtpClient.Port = 25;
            //        smtpClient.UseDefaultCredentials = false;
            //        NetworkCredential credentials = new NetworkCredential("<EMAIL>", "Factures@33");
            //        smtpClient.Credentials = credentials;

            //        mailMessage.From = new System.Net.Mail.MailAddress("<EMAIL>", "C T P");
            //        if (replyToCom)
            //        {
            //            mailMessage.ReplyTo = new System.Net.Mail.MailAddress("<EMAIL>", "C T P");
            //        }
            //        //message.From = new System.Net.Mail.MailAddress("<EMAIL>", "IntelSoft");
            //        //message.ReplyToList.Add("<EMAIL>");
            //        foreach (string recipient in recipients)
            //        {
            //            mailMessage.To.Add(recipient);
            //        }
            //        //mailMessage.To.Add("<EMAIL>");
            //        mailMessage.Subject = subject;
            //        mailMessage.Body = body;
            //        mailMessage.IsBodyHtml = isHtmlBody;

            //        //Adds the attachments
            //        if (fileName != null && fileName != "")
            //        {
            //            Attachment attachment = new Attachment(fileName);
            //            mailMessage.Attachments.Add(attachment);
            //        }

            //        // Send SMTP mail
            //        smtpClient.Send(mailMessage);

            //    }
            //    catch (Exception exp)
            //    {
            //        Data.CommonBusiness.AppExceptionsBusiness.LogException(exp);
            //        throw;
            //    }
            //    finally
            //    {
            //        if (mailMessage.Attachments.Count > 0)
            //        {
            //            mailMessage.Attachments.Dispose();
            //        }
            //        //File.Delete(fileName);
            //    }
            //}
        }


    }


    public static class MailUtility
    {
        //Extension method for MailMessage to save to a file on disk
        public static void Save(this MailMessage message, string filename, bool addUnsentHeader = true)
        {
            using (var filestream = File.Open(filename, FileMode.Create))
            {
                if (addUnsentHeader)
                {
                    var binaryWriter = new BinaryWriter(filestream);
                    //Write the Unsent header to the file so the mail client knows this mail must be presented in "New message" mode
                    binaryWriter.Write(System.Text.Encoding.UTF8.GetBytes("X-Unsent: 1" + Environment.NewLine));
                }

                var assembly = typeof(SmtpClient).Assembly;
                var mailWriterType = assembly.GetType("System.Net.Mail.MailWriter");

                // Get reflection info for MailWriter contructor
                var mailWriterContructor = mailWriterType.GetConstructor(BindingFlags.Instance | BindingFlags.NonPublic, null, new[] { typeof(Stream) }, null);

                // Construct MailWriter object with our FileStream
                var mailWriter = mailWriterContructor.Invoke(new object[] { filestream });

                // Get reflection info for Send() method on MailMessage
                var sendMethod = typeof(MailMessage).GetMethod("Send", BindingFlags.Instance | BindingFlags.NonPublic);

                sendMethod.Invoke(message, BindingFlags.Instance | BindingFlags.NonPublic, null, new object[] { mailWriter, true, true }, null);

                // Finally get reflection info for Close() method on our MailWriter
                var closeMethod = mailWriter.GetType().GetMethod("Close", BindingFlags.Instance | BindingFlags.NonPublic);

                // Call close method
                closeMethod.Invoke(mailWriter, BindingFlags.Instance | BindingFlags.NonPublic, null, new object[] { }, null);
            }
        }
    }
}