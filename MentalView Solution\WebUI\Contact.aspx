﻿<%@ Page Language="C#" MasterPageFile="~/Main.Master" EnableViewState="true" AutoEventWireup="true" CodeBehind="Contact.aspx.cs" ClientIDMode="Static" Inherits="WebUI.Contact" Culture="auto" meta:resourcekey="Page" UICulture="auto" %>


<%@ Register TagPrefix="ej" Namespace="Syncfusion.JavaScript.Web" Assembly="Syncfusion.EJ.Web" %>
<%@ Register TagPrefix="ej" Namespace="Syncfusion.JavaScript.Models" Assembly="Syncfusion.EJ" %>


<asp:Content ID="mainHeadContent" ContentPlaceHolderID="mainHead" runat="server">
    <script type="text/javascript">
        $(document).ready(function () {


            //var sampleArray = [{ id: 0, text: 'enhancement' }, { id: 1, text: 'bug' }
            //    , { id: 2, text: 'duplicate' }, { id: 3, text: 'invalid' }
            //    , { id: 4, text: 'wontfix' }];
            //$("#questionnaireCodeDDL").select2({ data: sampleArray });


            $('select').select2({ width: '100%' });
            $('select.no-search-select').select2({ width: '100%', minimumResultsForSearch: -1 });
            //$('select.no-search-select.multi-select').select2({ width: '100%', minimumResultsForSearch: -1, multiple: true });
            $('#newQuestionnaire2DDL').select2({ width: '300px', minimumResultsForSearch: -1 });

            $("#activeChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#waitingChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#melancholicChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#catatonicChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#therapyResistantChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#regressionChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#chronicChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#seasonalChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#epilochiaChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#pregnancyChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#elassonDepressiveDisorderChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#shortIntermmitentFormChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#premenstrualDysphoricDisorderChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#depressivePseudonoiaChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#generalizedAxietyChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#tikChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#paracumulationDisorderChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");
            $("#trichotillomaniaChkBox").bootstrapSwitch('onText', "<%= GetGlobalResourceObject("GlobalResources", "YesText").ToString() %>").bootstrapSwitch('offText', "<%= GetGlobalResourceObject("GlobalResources", "NoText").ToString() %>");

            //Εμφανίζει το επιλεγμένο tab από το serverside.
            var tabName = $('#selectedTab').val() ? $('#selectedTab').val() : 'generalTab';
            $('#Tabs a[href="#' + tabName + '"]').tab('show');
            $("#Tabs a").click(function () {
                $("[id*=TabName]").val($(this).attr("href").replace("#", ""));
            });
            $('#selectedTab').val('');

            $("#psychotherapyStartDateDiv").datepicker({ format: "dd/mm/yyyy", language: "el", autoclose: true });
            $("#birthDateDiv").datepicker({ format: "dd/mm/yyyy", language: "el", autoclose: true });
            $("#lastMedicalCheckupDateDiv").datepicker({ format: "dd/mm/yyyy", language: "el", autoclose: true });

            updateInactiveReasonDisability();

            //$("#questionnaireCodeDDL").ejDropDownList();


           <%-- var formattedBody = "<p>& Kappa;& alpha;& lambda;& eta;& mu; έ & rho;& alpha; & sigma;& alpha;& sigmaf;,</p>\n <p>& Theta; έ & lambda;& omicron;& upsilon;& mu;& epsilon; & nu;& alpha; & sigma;& alpha;& sigmaf; & epsilon;& nu;& eta;& mu;& epsilon;& rho; ώ & sigma;& epsilon;& iota; ό & tau;& iota;</p >\n < ul >\n < li >& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;</li >\n < li >& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;</li >\n < li >& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;& alpha;& sigma;& delta;& phi;</li >\n</ul >\n < p >& nbsp;</p >\n < p >& Epsilon;& upsilon;& chi;& alpha;& rho;& iota;& sigma;& tau;& omicron; ύ & mu;& epsilon; & pi;& omicron;& lambda; ύ, <br>Intelsoft</p>";
            var mailToLink = "mailto:<EMAIL>?subject=καλημέρα%20τι%20κανεις&body=<%= HttpUtility.UrlEncodeUnicode("<b>asdf</b>") %>";
            window.location.href = mailToLink;--%>
        });

        function Initialization() {

            ////Δημιουργεί το DropDown για το ContactId
            //$('select').select2({ width: '100%' });
            //$('select.no-search-select').select2({ width: '100%', minimumResultsForSearch: -1 });
            ////$('select.no-search-select.multi-select').select2({ width: '100%', minimumResultsForSearch: -1, multiple: true });
            //$('#newQuestionnaire2DDL').select2({ width: '300px', minimumResultsForSearch: -1 });

            SetLocalization();
        }

        function ShowNewQuestionnaireDialog() {
            $("#newQuestionnaireDialog").ejDialog("open");
        }

        function SendContactEmail1() {
            var email = $("#email1TxtBox").val();
            if (email) {
                var link = "mailto:" + email;

                window.location.href = link;
            }
            else {
                alert('To email δεν είναι συμπληρωμένο.');
            }
        }

        function SendContactEmail2() {
            var email = $("#email2TxtBox").val();
            if (email) {
                var link = "mailto:" + email;

                window.location.href = link;
            }
            else {
                alert('To email δεν είναι συμπληρωμένο.');
            }
        }

        function activeCheckChanged() {
            updateInactiveReasonDisability();
        }

        function updateInactiveReasonDisability() {
            var checked = $('#activeChkBox').is(':checked');
            if (checked == true) {
                $("#inactiveReasonDDL").prop('disabled', true);
            }
            else {
                $("#inactiveReasonDDL").prop('disabled', false);
            }
        }

        function OnRowDataBound(args) {
            //alert(args.data.Room);
            if (args.data.Room) {
                //alert(args.data.BackgroundColor);
                args.row.css("backgroundColor", args.data.BackgroundColor);/*custom css applied to the row */
            }
        }

        function createTextarea() {
            return "<textarea class='form-control' rows='5' style='width:100%'  >{{:QuestionnaireInput}}</textarea>";
        }

        function readTextarea(args) {
            return args.val();
        }

        function writeTextarea(args) {
            args.element.css("width", "100%");
        }

        function VisitQuestionnairiesLink() {
            var url = $("#questionnairiesLinkTxtBox").val();
            if (url) {
                window.open(url, '_blank');
            }
        }


        function emailTemplatesGridOnClick(args) {
            var grid = $("#emailTemplatesGrid").ejGrid("instance");
            var index = this.element.closest("tr").index();
            var record = grid.getCurrentViewData()[index];
            //alert("Record Details: " + JSON.stringify(record));

            // Open link in new window
            if (record && record.EmailTemplateTitle) {
                var title = record.EmailTemplateTitle;
                url = 'EmailTemplate?EmailTemplateTitle=' + title;
                window.open(url, '_blank', 'noopener,noreferrer');
            }
            return true;
        }
    </script>

    <script type="text/template" id="cellTooltipTemplate">
        {{:value }} 
    </script>

    <script type="text/x-template" id="notesColumnTemplate">
      <span style="white-space: break-spaces;">{{:Notes}}</span>
    </script>

    <script type="text/x-template" id="recurrenceColumnTemplate">
        {{if Recurrence}}
            <i class="fa fa-repeat">
        {{else}}
           
        {{/if}}
    </script>

    <script id="QuestionnaireTemplate" type="text/template">
        <div style="padding-right: 10px; padding-bottom: 10px;">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <span for="questionnaireNameTxtBox" runat="server" class="col-sm-3 control-label">Ονομασία:</span>
                            <div class="col-sm-9">
                                <input type="text" id="questionnaireNameTxtBox" name="QuestionnaireName" value="{{:Name}}" class="form-control" style="width: 100%; text-align: left !important;" maxlength="50" required />
                            </div>
                        </div>
                    </div>
                </div>
                <!--  <div class="col-md-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <span for="questionnaireIdDDL" runat="server" class="col-sm-3 control-label">Ονομασία 2:</span>
                            <div class="col-sm-9">
                                <select id="questionnaireCodeDDL" runat="server" name="QuestionnaireCodeDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select">
                                    <%-- <option  value=""></option>
                                    <option disabled value="1">Α. FOLLOW UPS</option>
                                    <option value="A1">Α1. Συνοπτικός δείκτης ποιότητας ζωής</option>
                                    <option value="A2">Α2. Ανατροφοδότηση προ συνεδρίας</option>
                                    <option value="A3">Α3. Ανατροφοδότηση μετά συνεδρίας</option>
                                    <option value="A4">Α4. Ανατροφοδότηση μετά ασύγχρονης </option>
                                    <option value="A5">Α5. Αναλυτικός δείκτης ποιότητας ζωής</option>
                                    <option disabled value="">Β.ΓΕΝΙΚΑ ΕΡΩΤΗΜΑΤΟΛΟΓΙΑ</option>--%>
                                </select>

                            </div>
                        </div>
                    </div>
                </div>  -->
                <div class="col-md-4">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <span for="questionnaireCreateDateTxtBox" runat="server" class="col-sm-3 control-label">Ημ/νία Δημιουργίας:</span>
                            <div class="col-sm-9">
                                <input id="questionnaireCreateDateTxtBox" name="CreateDate" value="{{:CreateDate}}" class="form-control" style="width: 100%;" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    Ερωτήσεις:
                <textarea type="text" class="form-control" id="questionnaireInputTxtBox" name="QuestionnaireInput" rows="10" maxlength="700">{{:QuestionnaireInput}}</textarea>
                </div>
            </div>
        </div>
    </script>

    <script>
        var currentQuestionnaireCodeValue = "";
        var currentEmailTemplateValue = "";

        function questionnairiesComplete(args) {
            $("#questionnaireCreateDateTxtBox").ejDatePicker({
                value: new Date(),
                locale: 'el-GR',
                buttonText: 'Σήμερα'
            });

            $('.e-inlineform-titlebar').hide();
            $("#questionnaireNameTxtBox").css('text-align', 'left');

            //alert(args.requestType);
            if (args.requestType == "beginedit" || args.requestType == "add") {
                var questionnaireCodesDataSource = [
                    { value: 'Ιστορικού', text: 'Ιστορικού' },
                    { value: 'Αναπτυξιακών - Μαθησιακών', text: 'Αναπτυξιακών - Μαθησιακών' },
                    { value: 'Ποιότητας ζωής', text: 'Ποιότητας ζωής' },
                    { value: 'Σχέσεων', text: 'Σχέσεων' },
                    { value: 'Διαθέσεων', text: 'Διαθέσεων' },
                    { value: 'Άγχους - Φοβιών', text: 'Άγχους - Φοβιών' },
                    { value: 'Συναισθήματος', text: 'Συναισθήματος' },
                    { value: 'Συμπεριφορών', text: 'Συμπεριφορών' },
                    { value: 'Προσωπικότητας', text: 'Προσωπικότητας' },
                    { value: 'Τραύματος', text: 'Τραύματος' }
                    //{ value: 'Α1. Συνοπτικός δείκτης ποιότητας ζωής', text: 'Α1. Συνοπτικός δείκτης ποιότητας ζωής' },
                    //{ value: 'Α2. Ανατροφοδότηση προ συνεδρίας', text: 'Α2. Ανατροφοδότηση προ συνεδρίας' },
                    //{ value: 'Α3. Ανατροφοδότηση μετά συνεδρίας', text: 'Α3. Ανατροφοδότηση μετά συνεδρίας' },
                    //{ value: 'Α4. Ανατροφοδότηση μετά ασύγχρονης', text: 'Α4. Ανατροφοδότηση μετά ασύγχρονης' },
                    //{ value: 'Α5. Αναλυτικός δείκτης ποιότητας ζωής', text: 'Α5. Αναλυτικός δείκτης ποιότητας ζωής' },
                    //{ value: 'Β1. Ερωτηματολόγιο - Διαχείριση Συγκρούσεων', text: 'Β1. Ερωτηματολόγιο - Διαχείριση Συγκρούσεων' },
                    //{ value: 'Β2. Ερωτηματολόγιο - OCD', text: 'Β2. Ερωτηματολόγιο - OCD' },
                    //{ value: 'Β3. Ερωτηματολόγιο - ADHD', text: 'Β3. Ερωτηματολόγιο - ADHD' },
                    //{ value: 'Β4. Ερωτηματολόγιο - Κρίσεις Πανικού', text: 'Β4. Ερωτηματολόγιο - Κρίσεις Πανικού' },
                    //{ value: 'Β5. Ερωτηματολόγιο - Beck Anxiety Inventory (BAI)', text: 'Β5. Ερωτηματολόγιο - Beck Anxiety Inventory (BAI)' },
                    //{ value: 'Β6. Ερωτηματολόγιο - NPD', text: 'Β6. Ερωτηματολόγιο - NPD' },
                    //{ value: 'Β7. Ερωτηματολόγιο - BPD', text: 'Β7. Ερωτηματολόγιο - BPD' },
                    //{ value: 'Β8. Ερωτηματολόγιο - M-BD', text: 'Β8. Ερωτηματολόγιο - M-BD' },
                    //{ value: 'Β9. Ερωτηματολόγιο - MDQ', text: 'Β9. Ερωτηματολόγιο - MDQ' },
                    //{ value: 'Β10. Ερωτηματολόγιο - Λίστα Προβλημάτων', text: 'Β10. Ερωτηματολόγιο - Λίστα Προβλημάτων' },
                    //{ value: 'Β11. Liebowitz Social Anxiety Scale', text: 'Β11. Liebowitz Social Anxiety Scale' },
                    //{ value: 'Β12. Ερωτηματολόγιο - Μηχανισμοί άμυνας', text: 'Β12. Ερωτηματολόγιο - Μηχανισμοί άμυνας' },
                    //{ value: 'Β13. Ερωτηματολόγιο - Αλεξιθυμία', text: 'Β13. Ερωτηματολόγιο - Αλεξιθυμία' },
                    //{ value: 'Β14. Social Readjustment Rating Scale', text: 'Β14. Social Readjustment Rating Scale' },
                    //{ value: 'Β15. Ερωτηματολόγιο - HADS', text: 'Β15. Ερωτηματολόγιο - HADS' },
                    //{ value: 'Β16. Ερωτηματολόγιο - Q.O.L.', text: 'Β16. Ερωτηματολόγιο - Q.O.L.' },
                    //{ value: 'Β17. Ερωτηματολόγιο - B.D.I. Becks Depression Inventory', text: 'Β17. Ερωτηματολόγιο - B.D.I. Becks Depression Inventory' },
                    //{ value: 'Β18. Άσκηση Μη - Βίαιης Επικοινωνίας', text: 'Β18. Άσκηση Μη - Βίαιης Επικοινωνίας' },
                    //{ value: 'Β19. Dissociation Experience Scale - (DES)', text: 'Β19. Dissociation Experience Scale - (DES)' },
                    //{ value: 'Β20. Αναθεωρημένη Κλίμακα Εκτίμησης της Ψυχολογικής Επίδρασης Στρεσογόνου Κατάστασης - (RIES)', text: 'Β20. Αναθεωρημένη Κλίμακα Εκτίμησης της Ψυχολογικής Επίδρασης Στρεσογόνου Κατάστασης - (RIES)' },
                    //{ value: 'Γ1. Ποιότητα Ζωής', text: 'Γ1. Ποιότητα Ζωής' },
                    //{ value: 'Γ2. Διάθεση', text: 'Γ2. Διάθεση' },
                    //{ value: 'Γ3. Εαυτός', text: 'Γ3. Εαυτός' },
                    //{ value: 'Γ4. Σχέσεις', text: 'Γ4. Σχέσεις' },
                    //{ value: 'Γ5. Ψυχική Ανθεκτικότητα', text: 'Γ5. Ψυχική Ανθεκτικότητα' },
                    //{ value: 'Γ6. Αίσθημα Ελπίδας', text: 'Γ6. Αίσθημα Ελπίδας' },
                    //{ value: 'Γ7. Συναισθηματική Ανοιχτότητα', text: 'Γ7. Συναισθηματική Ανοιχτότητα' },
                    //{ value: 'Γ8. Διεκδικητική Συμπεριφορά', text: 'Γ8. Διεκδικητική Συμπεριφορά' },
                    //{ value: 'Γ9. Σεβασμός', text: 'Γ9. Σεβασμός' },
                    //{ value: 'Γ9. Αυτοφροντίδα', text: 'Γ9. Αυτοφροντίδα' },
                    //{ value: 'Δ1. Σχήματα 1 & 2 ', text: 'Δ1. Σχήματα 1 & 2 ' },
                    //{ value: 'Δ2. Σχήματα 3 & 4', text: 'Δ2. Σχήματα 3 & 4' },
                    //{ value: 'Δ3. Σχήματα 5 & 6', text: 'Δ3. Σχήματα 5 & 6' },
                    //{ value: 'Δ4. Σχήματα 7 & 8', text: 'Δ4. Σχήματα 7 & 8' },
                    //{ value: 'Δ5. Σχήματα 9 & 10', text: 'Δ5. Σχήματα 9 & 10' },
                    //{ value: 'Δ6. Σχήματα 11 & 12', text: 'Δ6. Σχήματα 11 & 12' },
                    //{ value: 'Δ7. Σχήματα 13 & 14', text: 'Δ7. Σχήματα 13 & 14' },
                    //{ value: 'Δ8. Σχήματα 15 & 16', text: 'Δ8. Σχήματα 15 & 16' },
                    //{ value: 'Δ9. Σχήματα 17 & 18', text: 'Δ9. Σχήματα 17 & 18' },
                    //{ value: 'Ε1. Πλαίσιο οικογενειακής καταγωγής 1', text: 'Ε1. Πλαίσιο οικογενειακής καταγωγής 1' },
                    //{ value: 'Ε2. Πλαίσιο οικογενειακής καταγωγής 2', text: 'Ε2. Πλαίσιο οικογενειακής καταγωγής 2' },
                    //{ value: 'ΣΤ1. Εννεάγραμμα 1', text: 'ΣΤ1. Εννεάγραμμα 1' },
                    //{ value: 'ΣΤ2. Εννεάγραμμα 2', text: 'ΣΤ2. Εννεάγραμμα 2' },
                    //{ value: 'ΣΤ3. 16 PERSONALITY', text: 'ΣΤ3. 16 PERSONALITY' },
                    //{ value: 'Ζ1. Ερωτηματολόγια E.I. - Q. 20', text: 'Ζ1. Ερωτηματολόγια E.I. - Q. 20' },
                    //{ value: 'Ζ2. Ερωτηματολόγια E.I. - Q. 30', text: 'Ζ2. Ερωτηματολόγια E.I. - Q. 30' },
                    //{ value: 'Η1. Ερωτηματολόγιο - Αξιολόγηση σχέσης', text: 'Η1. Ερωτηματολόγιο - Αξιολόγηση σχέσης' },
                    //{ value: 'Η2. Οι εμπειρίες στις στενές σχέσεις - (ECR-R)', text: 'Η2. Οι εμπειρίες στις στενές σχέσεις - (ECR-R)' },
                    //{ value: 'Θ1. Άσκηση διεκδικητικής συμπεριφοράς', text: 'Θ1. Άσκηση διεκδικητικής συμπεριφοράς' },
                    //{ value: 'Θ2. Ερωτηματολόγιο - Διεκδικητική συμπεριφορά', text: 'Θ2. Ερωτηματολόγιο - Διεκδικητική συμπεριφορά' },
                    //{ value: 'Θ3. Άσκηση αυτό - αξιολόγησης και ανακάλυψης διεκδικητικής συμπεριφοράς', text: 'Θ3. Άσκηση αυτό - αξιολόγησης και ανακάλυψης διεκδικητικής συμπεριφοράς' }

                ];

                $("#ctl00_ctl00_includeFilesBody_mainBody_questionnairiesGridQuestionnaireCode").ejDropDownList({ dataSource: questionnaireCodesDataSource }); //Set the new Dropdown datasource
                $("#ctl00_ctl00_includeFilesBody_mainBody_questionnairiesGridQuestionnaireCode").ejDropDownList("setSelectedText", currentQuestionnaireCodeValue); //Set the value
            }
        }

        function questionnairiesBeforeEdit(args) {
            currentQuestionnaireCodeValue = args.rowData.QuestionnaireCode;  //Αποθηκεύει τη τιμή για να τη χρησιμοποιήσει στο beginedit event.
        }

        function emailTemplatesComplete(args) {

            $("#ctl00_ctl00_includeFilesBody_mainBody_emailTemplatesGridDate").ejDatePicker({
                value: new Date(),
                locale: 'el-GR',
                buttonText: 'Σήμερα'
            });

            $('.e-inlineform-titlebar').hide();
            //$("#emailTemplatesTitleTxtBox").css('text-align', 'left');

            //alert(args.requestType);
            if (args.requestType == "beginedit" || args.requestType == "add") {
                var emailTemplatesDataSource = [
                    { value: 'Α1. Συνοπτικός δείκτης ποιότητας ζωής', text: 'Α1. Συνοπτικός δείκτης ποιότητας ζωής' },
                    { value: 'Α2. Ανατροφοδότηση προ συνεδρίας', text: 'Α2. Ανατροφοδότηση προ συνεδρίας' },
                    { value: 'Α3. Ανατροφοδότηση μετά συνεδρίας', text: 'Α3. Ανατροφοδότηση μετά συνεδρίας' },
                    { value: 'Α4. Ανατροφοδότηση μετά ασύγχρονης', text: 'Α4. Ανατροφοδότηση μετά ασύγχρονης' },
                    { value: 'Α5. Αναλυτικός δείκτης ποιότητας ζωής', text: 'Α5. Αναλυτικός δείκτης ποιότητας ζωής' },
                    { value: 'Β1. Ερωτηματολόγιο - Διαχείριση Συγκρούσεων', text: 'Β1. Ερωτηματολόγιο - Διαχείριση Συγκρούσεων' },
                    { value: 'Β2. Ερωτηματολόγιο - OCD', text: 'Β2. Ερωτηματολόγιο - OCD' },
                    { value: 'Β3. Ερωτηματολόγιο - ADHD', text: 'Β3. Ερωτηματολόγιο - ADHD' },
                    { value: 'Β4. Ερωτηματολόγιο - Κρίσεις Πανικού', text: 'Β4. Ερωτηματολόγιο - Κρίσεις Πανικού' },
                    { value: 'Β5. Ερωτηματολόγιο - Beck Anxiety Inventory (BAI)', text: 'Β5. Ερωτηματολόγιο - Beck Anxiety Inventory (BAI)' },
                    { value: 'Β6. Ερωτηματολόγιο - NPD', text: 'Β6. Ερωτηματολόγιο - NPD' },
                    { value: 'Β7. Ερωτηματολόγιο - BPD', text: 'Β7. Ερωτηματολόγιο - BPD' },
                    { value: 'Β8. Ερωτηματολόγιο - M-BD', text: 'Β8. Ερωτηματολόγιο - M-BD' },
                    { value: 'Β9. Ερωτηματολόγιο - MDQ', text: 'Β9. Ερωτηματολόγιο - MDQ' },
                    { value: 'Β10. Ερωτηματολόγιο - Λίστα Προβλημάτων', text: 'Β10. Ερωτηματολόγιο - Λίστα Προβλημάτων' },
                    { value: 'Β11. Liebowitz Social Anxiety Scale', text: 'Β11. Liebowitz Social Anxiety Scale' },
                    { value: 'Β12. Ερωτηματολόγιο - Μηχανισμοί άμυνας', text: 'Β12. Ερωτηματολόγιο - Μηχανισμοί άμυνας' },
                    { value: 'Β13. Ερωτηματολόγιο - Αλεξιθυμία', text: 'Β13. Ερωτηματολόγιο - Αλεξιθυμία' },
                    { value: 'Β14. Social Readjustment Rating Scale', text: 'Β14. Social Readjustment Rating Scale' },
                    { value: 'Β15. Ερωτηματολόγιο - HADS', text: 'Β15. Ερωτηματολόγιο - HADS' },
                    { value: 'Β16. Ερωτηματολόγιο - Q.O.L.', text: 'Β16. Ερωτηματολόγιο - Q.O.L.' },
                    { value: 'Β17. Ερωτηματολόγιο - B.D.I. Becks Depression Inventory', text: 'Β17. Ερωτηματολόγιο - B.D.I. Becks Depression Inventory' },
                    { value: 'Β18. Άσκηση Μη - Βίαιης Επικοινωνίας', text: 'Β18. Άσκηση Μη - Βίαιης Επικοινωνίας' },
                    { value: 'Β19. Dissociation Experience Scale - (DES)', text: 'Β19. Dissociation Experience Scale - (DES)' },
                    { value: 'Β20. Αναθεωρημένη Κλίμακα Εκτίμησης της Ψυχολογικής Επίδρασης Στρεσογόνου Κατάστασης - (RIES)', text: 'Β20. Αναθεωρημένη Κλίμακα Εκτίμησης της Ψυχολογικής Επίδρασης Στρεσογόνου Κατάστασης - (RIES)' },
                    { value: 'Γ1. Ποιότητα Ζωής', text: 'Γ1. Ποιότητα Ζωής' },
                    { value: 'Γ2. Διάθεση', text: 'Γ2. Διάθεση' },
                    { value: 'Γ3. Εαυτός', text: 'Γ3. Εαυτός' },
                    { value: 'Γ4. Σχέσεις', text: 'Γ4. Σχέσεις' },
                    { value: 'Γ5. Ψυχική Ανθεκτικότητα', text: 'Γ5. Ψυχική Ανθεκτικότητα' },
                    { value: 'Γ6. Αίσθημα Ελπίδας', text: 'Γ6. Αίσθημα Ελπίδας' },
                    { value: 'Γ7. Συναισθηματική Ανοιχτότητα', text: 'Γ7. Συναισθηματική Ανοιχτότητα' },
                    { value: 'Γ8. Διεκδικητική Συμπεριφορά', text: 'Γ8. Διεκδικητική Συμπεριφορά' },
                    { value: 'Γ9. Σεβασμός', text: 'Γ9. Σεβασμός' },
                    { value: 'Γ9. Αυτοφροντίδα', text: 'Γ9. Αυτοφροντίδα' },
                    { value: 'Δ1. Σχήματα 1 & 2 ', text: 'Δ1. Σχήματα 1 & 2 ' },
                    { value: 'Δ2. Σχήματα 3 & 4', text: 'Δ2. Σχήματα 3 & 4' },
                    { value: 'Δ3. Σχήματα 5 & 6', text: 'Δ3. Σχήματα 5 & 6' },
                    { value: 'Δ4. Σχήματα 7 & 8', text: 'Δ4. Σχήματα 7 & 8' },
                    { value: 'Δ5. Σχήματα 9 & 10', text: 'Δ5. Σχήματα 9 & 10' },
                    { value: 'Δ6. Σχήματα 11 & 12', text: 'Δ6. Σχήματα 11 & 12' },
                    { value: 'Δ7. Σχήματα 13 & 14', text: 'Δ7. Σχήματα 13 & 14' },
                    { value: 'Δ8. Σχήματα 15 & 16', text: 'Δ8. Σχήματα 15 & 16' },
                    { value: 'Δ9. Σχήματα 17 & 18', text: 'Δ9. Σχήματα 17 & 18' },
                    { value: 'Ε1. Πλαίσιο οικογενειακής καταγωγής 1', text: 'Ε1. Πλαίσιο οικογενειακής καταγωγής 1' },
                    { value: 'Ε2. Πλαίσιο οικογενειακής καταγωγής 2', text: 'Ε2. Πλαίσιο οικογενειακής καταγωγής 2' },
                    { value: 'ΣΤ1. Εννεάγραμμα 1', text: 'ΣΤ1. Εννεάγραμμα 1' },
                    { value: 'ΣΤ2. Εννεάγραμμα 2', text: 'ΣΤ2. Εννεάγραμμα 2' },
                    { value: 'ΣΤ3. 16 PERSONALITY', text: 'ΣΤ3. 16 PERSONALITY' },
                    { value: 'Ζ1. Ερωτηματολόγια E.I. - Q. 20', text: 'Ζ1. Ερωτηματολόγια E.I. - Q. 20' },
                    { value: 'Ζ2. Ερωτηματολόγια E.I. - Q. 30', text: 'Ζ2. Ερωτηματολόγια E.I. - Q. 30' },
                    { value: 'Η1. Ερωτηματολόγιο - Αξιολόγηση σχέσης', text: 'Η1. Ερωτηματολόγιο - Αξιολόγηση σχέσης' },
                    { value: 'Η2. Οι εμπειρίες στις στενές σχέσεις - (ECR-R)', text: 'Η2. Οι εμπειρίες στις στενές σχέσεις - (ECR-R)' },
                    { value: 'Θ1. Άσκηση διεκδικητικής συμπεριφοράς', text: 'Θ1. Άσκηση διεκδικητικής συμπεριφοράς' },
                    { value: 'Θ2. Ερωτηματολόγιο - Διεκδικητική συμπεριφορά', text: 'Θ2. Ερωτηματολόγιο - Διεκδικητική συμπεριφορά' },
                    { value: 'Θ3. Άσκηση αυτό - αξιολόγησης και ανακάλυψης διεκδικητικής συμπεριφοράς', text: 'Θ3. Άσκηση αυτό - αξιολόγησης και ανακάλυψης διεκδικητικής συμπεριφοράς' }
                ];

                emailTemplatesDataSource = $("#emailTemplatesDataSourceHiddenFld").val();
                alert(emailTemplatesDataSource);
                //$("#ctl00_ctl00_includeFilesBody_mainBody_emailTemplatesGridEmailTemplateTitle").ejDropDownList({ dataSource: emailTemplatesDataSource }); //Set the new Dropdown datasource
                //$("#ctl00_ctl00_includeFilesBody_mainBody_emailTemplatesGridEmailTemplateTitle").ejDropDownList("setSelectedText", currentEmailTemplateValue); //Set the value
            }

        }

        function emailTemplatesBeforeEdit(args) {
            currentEmailTemplateValue = args.rowData.ContactEmailTemplateId;  //Αποθηκεύει τη τιμή για να τη χρησιμοποιήσει στο beginedit event.
        }
    </script>

    <script type="text/javascript">
        function openContactReportDialog() {
            $("#contactReportDialog").ejDialog("open");
        }

        function openEmailTemplatesDialog() {
            $("#emailTemplatesDialog").ejDialog("open");
        }
    </script>

    <script type="text/javascript">
        function FixInputLettersInQuestionnaireName() {
            $("#questionnaireNameTxtBox").val($("#questionnaireNameTxtBox").val().replace(/[^A-Za-z0-9 ]/ig, ''));
        }
    </script>

    <script type="text/javascript">

      <%--  function AiDiagnosisAndTherapy() {
            var tenantId = 1;
            var contactId = 23;
            $.ajax({
                type: "POST",
                url: "AiProvider.aspx/AiDiagnosisAndTherapy",
                //data: "{'tenantId': " + tenantId + ",'contactId': " + contactId + "}",
                data: JSON.stringify('tenantId: {tenantId}, contactId: {contactId}'),
                contentType: "application/json; charset=utf-8",
                dataType: "json",
                async: false,
                cache: false,
                xhrFields: {
                    withCredentials: true  // This ensures cookies are sent with the request
                },
                headers: {
                    'Authorization': 'Bearer ' + getCookie(".ASPXAUTH")
                },
                //beforeSend: function (xhr) {
                //    // Optional: Add any additional headers if needed
                //    var token = getTokenFromCookie();
                //    alert(token);
                //    xhr.setRequestHeader("Authorization", "Bearer " + token);
                //},
                success: function (msg) {
                    console.log(msg.responseText);


                },
                error: function (msg) {
                    alert(msg.responseText);
                }
            });
            return false;
        }

        function getCookie(name) {
            var value = "; " + document.cookie;
            var parts = value.split("; " + name + "=");
            if (parts.length == 2) return parts.pop().split(";").shift();
        }

        function showAiDisplayDialog(text) {
            $("#<%= aiResponseTxtBox.ClientID %>").text("");
            $("#aiDisplayDialog").ejDialog("open");
        }--%>

        function printDialogText() {
            var content = $("#<%= aiResponseTxtBox.ClientID %>").text();
            var printWindow = window.open('', '', 'height=500,width=800');
            printWindow.document.write('<html><head><title>Print</title>');
            printWindow.document.write('</head><body >');
            printWindow.document.write(content);
            printWindow.document.write('</body></html>');
            printWindow.document.close();
            printWindow.print();
        }

        //function getTokenFromCookie() {
        //    try {
        //        alert('a');
        //        const cookies = document.cookie.split(';');
        //        const aspxAuthCookie = cookies.find(cookie =>
        //            cookie.trim().startsWith('.ASPXAUTH=')
        //        );

        //        return aspxAuthCookie
        //            ? aspxAuthCookie.trim().substring('.ASPXAUTH='.length)
        //            : null;
        //    } catch (error) {
        //        console.error('Error retrieving .ASPXAUTH cookie:', error);
        //        return null;
        //    }
        //}

    </script>
</asp:Content>
<asp:Content ID="mainBodyContent" ContentPlaceHolderID="mainBody" runat="server">
    <%--<asp:ScriptManagerProxy ID="ScriptManagerProxy1" runat="server"></asp:ScriptManagerProxy>--%>
    <div class="row">
        <div class="col-xs-8">
            <div runat="server" id="saveBtnGroup" class="btn-group">
                <asp:LinkButton ID="saveCloseBtn" runat="server" CssClass="btn btn-primary btn-flat margin-bottom" OnClick="saveCloseBtn_Click"><span class="hidden-md hidden-lg"><i class="fa fa-save"></i></span><asp:Label Text="<%$ Resources:GlobalResources, SaveClose %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
                <button type="button" class="btn btn-primary btn-flat dropdown-toggle" data-toggle="dropdown">
                    <span class="caret"></span>
                    <span class="sr-only">Toggle Dropdown</span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <%--<asp:Button ID="" CssClass="btn btn-flat" style="background: transparent;" runat="server" Text="<%$ Resources:GlobalResources, Save %>" OnClick="saveBtn_Click"></asp:Button>--%>
                        <asp:LinkButton ID="saveBtn" runat="server" Text="<%$ Resources:GlobalResources, Save %>" OnClick="saveBtn_Click"></asp:LinkButton>
                    </li>
                </ul>
            </div>
            <asp:LinkButton ID="deleteBtn" runat="server" DisableValidation="True" CssClass="btn btn-danger btn-flat margin-bottom margin-r-5" OnClick="deleteBtn_Click" CausesValidation="False"><span class="hidden-md hidden-lg"><i class="fa fa-trash"></i></span><asp:Label Text="<%$ Resources:GlobalResources, Delete %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
            <asp:LinkButton ID="closeBtn" runat="server" DisableValidation="True" CssClass="btn btn-default btn-flat margin-bottom margin-r-5" OnClick="closeBtn_Click" CausesValidation="False" PostBackUrl="~/Contacts.aspx"><span class="hidden-md hidden-lg"><i class="fa fa-remove "></i></span><asp:Label Text="<%$ Resources:GlobalResources, Close %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></asp:LinkButton>
        </div>
        <div class="col-cs-4">
            <div class="btn-group margin-bottom margin-r-5 pull-right">
                <button type="button" id="appointmentReportBtn" runat="server" class="btn btn-default btn-flat margin-r-5" onclick="openContactReportDialog(); return false;">
                    <span class="hidden-md hidden-lg"><i class="fa fa-print"></i></span>
                    <asp:Label meta:resourceKey="contactReportLbl" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label>
                </button>
                <button type="button" id="sendEmailTemplateBtn" runat="server" class="btn btn-default btn-flat" onclick="openEmailTemplatesDialog(); return false;">
                    <span class="hidden-md hidden-lg"><i class="fa fa-envelope"></i></span>
                    <asp:Label meta:resourceKey="sendEmailLbl" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label>
                </button>

                <%--<button type="button" id="appointmentReportBtn" runat="server" class="btn btn-default btn-flat dropdown-toggle" data-toggle="dropdown" aria-expanded="true">
                    <asp:Label runat="server" meta:resourcekey="contactReportLbl"></asp:Label>
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" role="menu">
                    <li>
                        <asp:LinkButton ID="previewContactReportBtn" runat="server" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewContactReportBtn_Click"></asp:LinkButton>
                    </li>
                    <li>
                        <asp:LinkButton ID="exportContactReportToPdfBtn" runat="server" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="exportContactReportToPdfBtn_Click"></asp:LinkButton>
                    </li>
                    <li>
                        <button id="LinkButton1" runat="server" onclick="openContactReportDialog(); return false;">asdf</button>
                    </li>
                </ul>--%>
            </div>
        </div>
    </div>

    <ul id="Tabs" class="nav nav-tabs">
        <li><a href="#generalTab" data-toggle="tab">Γενικές</a></li>
        <li><a href="#personalAndMedicalInfoTab" data-toggle="tab">Ατομικές & Ιατρικές</a></li>
        <li><a href="#biographicalInfoTab" data-toggle="tab">Βιογραφικές</a></li>
        <li><a href="#clinicalViewInfoTab" data-toggle="tab">Κλινική Εικόνα</a></li>
        <li><a href="#questionnairesTab" data-toggle="tab">Ερωτηματολόγια</a></li>
        <li><a href="#appointmentsTab" data-toggle="tab">Συνεδρίες</a></li>
        <li><a href="#aiTab" data-toggle="tab">Chat GPT</a></li>
    </ul>
    <div class="tab-content">
        <div runat="server" class="tab-pane" id="generalTab">
            <br />
            <div class="row">
                <div class="col-sm-6">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="lastNameTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="lastNameLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class="input-group">
                                            <input type="text" runat="server" class="form-control" id="lastNameTxtBox" maxlength="50" required="required">
                                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="firstNameTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="firstNameLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class="input-group">
                                            <input type="text" runat="server" class="form-control " id="firstNameTxtBox" maxlength="50" required="required">
                                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="contactCodeTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="contactCodeLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input type="text" runat="server" class="form-control " id="contactCodeTxtBox" maxlength="20" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="appointmentCategoryIdDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="appointmentCategoryIdLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <ej:DropDownList ID="appointmentCategoryIdDDL" runat="server" WatermarkText="" Width="100%">
                                        </ej:DropDownList>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="stateDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="stateLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <ej:DropDownList ID="stateDDL" runat="server" WatermarkText="" Width="100%">
                                        </ej:DropDownList>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="activeChkBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="activeLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input runat="server" type="checkbox" id="activeChkBox" onchange="activeCheckChanged()" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="inactiveReasonDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="inactiveReasonLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="inactiveReasonDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="waitingChkBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="waitingLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input runat="server" type="checkbox" id="waitingChkBox" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="therapistIdDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="therapistIdLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="therapistIdDDL" datavaluefield="UserId" datatextfield="FullName" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="coTherapistIdDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="coTherapistIdDDL"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="coTherapistIdDDL" datavaluefield="UserId" datatextfield="FullName" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="guestIdDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="guestIdLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="guestIdDDL" datavaluefield="UserId" datatextfield="FullName" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="diagnosticianIdDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="diagnosticianIdLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="diagnosticianIdDDL" datavaluefield="UserId" datatextfield="FullName" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="clinicSupervisorIdDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="clinicSupervisorIdLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="clinicSupervisorIdDDL" datavaluefield="UserId" datatextfield="FullName" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="psychotherapyStartDateTextBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="psychotherapyStartDateLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class='input-group date' id='psychotherapyStartDateDiv'>
                                            <asp:TextBox runat="server" ID="psychotherapyStartDateTextBox" CssClass="form-control squa"></asp:TextBox>
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="referralToAnotherSpecialistDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="referralToAnotherSpecialistLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="referralToAnotherSpecialistDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="sessionOriginDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="sessionOriginLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="sessionOriginDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="sexDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="sexLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="sexDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="mobile1TxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="mobile1Lbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class="input-group">
                                            <input type="text" runat="server" class="form-control" id="mobile1TxtBox" maxlength="20" required="required">
                                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="phone1TxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="phone1Lbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input type="text" runat="server" class="form-control" id="phone1TxtBox" maxlength="20">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="emergencyContactNameTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="emergencyContactNameLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input type="text" runat="server" class="form-control" id="emergencyContactNameTxtBox" maxlength="50">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="emergencyPhoneTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="emergencyPhoneLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input type="text" runat="server" class="form-control" id="emergencyPhoneTxtBox" maxlength="50">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="email1TxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="email1Lbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class='input-group'>
                                            <input type="text" runat="server" class="form-control" id="email1TxtBox" maxlength="50" required>
                                            <span class="input-group-addon">
                                                <a id="sendEmail1Btn" runat="server" cssclass="btn btn-default btn-flat" style="cursor: default;" onclick="SendContactEmail1(); return false;"><span class="hidden-md hidden-lg"><i class="fa fa-envelope"></i></span>
                                                    <asp:Label meta:resourcekey="sendEmail1Btn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></a>
                                            </span>
                                            <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="email2TxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="email2Lbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class='input-group'>
                                            <input type="text" runat="server" class="form-control" id="email2TxtBox" maxlength="50" required>
                                            <span class="input-group-addon">
                                                <a id="sendEmail2Btn" runat="server" cssclass="btn btn-default btn-flat" style="cursor: default;" onclick="SendContactEmail2(); return false;"><span class="hidden-md hidden-lg"><i class="fa fa-envelope"></i></span>
                                                    <asp:Label meta:resourcekey="sendEmail2Btn" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></a>
                                            </span>
                                            <%--           <span class="input-group-addon borderless required-mark"><i class="fa fa-asterisk "></i></span>--%>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="birthDateTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="birthDateLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class='input-group date' id='birthDateDiv'>
                                            <asp:TextBox runat="server" ID="birthDateTxtBox" CssClass="form-control squa"></asp:TextBox>
                                            <span class="input-group-addon">
                                                <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="birthPlaceTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="birthPlaceLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input type="text" runat="server" class="form-control" id="birthPlaceTxtBox" maxlength="50">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="residenceTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="residenceLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="residenceTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="educationLevelDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="educationLevelLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="educationLevelDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="maritalStatusDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="maritalStatusLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="maritalStatusDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="childrenTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="childrenLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="childrenTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="childrenAgeTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="childrenAgeLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <input runat="server" class="form-control" id="childrenAgeTxtBox" maxlength="500">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="livingStatusDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="livingStatusLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="livingStatusDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="occupationDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="occupationLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="occupationDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="economicStatusDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="economicStatusLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="economicStatusDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="communicationMethodDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="communicationMethodLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="communicationMethodDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="sessionFrequencyDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="sessionFrequencyLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="sessionFrequencyDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="accessLevelDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="accessLevelLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="accessLevelDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="notesTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="notesLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <div class="input-group">
                                            <asp:TextBox runat="server" ID="notesTxtBox" class="form-control" TextMode="MultiLine" Rows="10" MaxLength="15000" Style="resize: none; height: inherit;" onblur="speechTextBoxBlur('notesTxtBox')"></asp:TextBox>
                                            <div class="input-group-addon borderless" style="padding-left: 30px">
                                                <i id="notesMicIcon" class="fas fa-microphone mic-icon" onclick="toggleSpeech('notesTxtBox')"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-6">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="caseFormulationTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="caseFormulationLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <asp:TextBox runat="server" ID="caseFormulationTxtBox" class="form-control" TextMode="MultiLine" Rows="30" MaxLength="15000" Style="resize: none; height: inherit;"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="specilistObservationsTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="specilistObservationsLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <asp:TextBox runat="server" ID="specilistObservationsTxtBox" class="form-control" TextMode="MultiLine" Rows="30" MaxLength="15000" Style="resize: none; height: inherit;"></asp:TextBox>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="skillsTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="skillsLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="skillsTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="traumaticHistoryTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="traumaticHistoryLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="traumaticHistoryTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="negativeBeliefsTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="negativeBeliefsLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="negativeBeliefsTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="negativeEmotionsTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="negativeEmotionsLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="negativeEmotionsTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="triggeringEventsTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="triggeringEventsLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="triggeringEventsTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="dysfunctionalBehaviorsTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="dysfunctionalBehaviorsLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="dysfunctionalBehaviorsTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="secondaryBenefitTxtBox" runat="server" class="col-sm-3 control-label" meta:resourcekey="secondaryBenefitLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <textarea runat="server" class="form-control" id="secondaryBenefitTxtBox" maxlength="500"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label for="emotionalProfileDDL" runat="server" class="col-sm-3 control-label" meta:resourcekey="emotionalProfileLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <select runat="server" id="emotionalProfileDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-horizontal">
                                <div class="form-group">
                                    <asp:Label ID="Label1" runat="server" class="col-sm-3 control-label" meta:resourcekey="contactEmailTemplatesLbl"></asp:Label>
                                    <div class="col-sm-9">
                                        <ej:Grid ID="emailTemplatesGrid" runat="server" EnableViewState="true" Locale="el-GR" IsResponsive="true" EnableResponsiveRow="true" EnableRowHover="False" OnServerAddRow="emailTemplatesGrid_ServerAddRow" OnServerEditRow="emailTemplatesGrid_ServerEditRow" OnServerDeleteRow="emailTemplatesGrid_ServerDeleteRow">
                                            <SortedColumns>
                                                <ej:SortedColumn Field="Date" Direction="Descending" />
                                            </SortedColumns>
                                            <ToolbarSettings ShowToolbar="true" ToolbarItems="add,edit,delete,update,cancel">
                                            </ToolbarSettings>
                                            <EditSettings AllowEditing="true" AllowAdding="true" AllowDeleting="true" EditMode="Normal" ShowConfirmDialog="true"></EditSettings>
                                            <%--<ClientSideEvents ActionComplete="emailTemplatesComplete" BeginEdit="emailTemplatesBeforeEdit" />--%>
                                            <Columns>
                                                <ej:Column Field="EmailTemplateTitle" EditType="DropdownEdit" HeaderText="Ονομασία" Width="250">
                                                    <DropDownEditOptions Text="String" ItemValue="String" />
                                                </ej:Column>
                                                <ej:Column Field="Date" EditType="Datepicker" Format="{0:dd/MM/yyyy}" HeaderText="Ημ/νία" Width="110">
                                                    <ValidationRule>
                                                        <ej:KeyValue Key="required" Value="true" />
                                                    </ValidationRule>
                                                </ej:Column>
                                                <ej:Column Field="Status" EditType="DropdownEdit" HeaderText="Κατάσταση">
                                                    <ValidationRule>
                                                        <ej:KeyValue Key="maxlength" Value="30" />
                                                    </ValidationRule>
                                                    <DropDownEditOptions Text="String" ItemValue="String" />
                                                </ej:Column>
                                                <ej:Column HeaderText=" " IsUnbound="True" Width="80" AllowResizing="False" AllowTextWrap="False">
                                                    <Command>
                                                        <%--<ej:Commands Type="OpenTemplate">
                                                            <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                                                        </ej:Commands>--%>
                                                        <ej:Commands Type="">
                                                            <ButtonOptions Text="..." Width="40" Click="emailTemplatesGridOnClick"></ButtonOptions>
                                                        </ej:Commands>
                                                    </Command>
                                                </ej:Column>
                                                <ej:Column Field="ContactEmailTemplateId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                                                </ej:Column>
                                            </Columns>
                                        </ej:Grid>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div runat="server" class="tab-pane" id="personalAndMedicalInfoTab">
            <br />
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="eyeContactDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="eyeContactLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="eyeContactDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="bodyLanguageDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="bodyLanguageLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="bodyLanguageDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="voiceToneDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="voiceToneLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="voiceToneDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="narrationDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="narrationLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="narrationDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="sexualOrientationDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="sexualOrientationLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="sexualOrientationDDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="generalRequestDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="generalRequestLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="generalRequestDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="specialRequestTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="specialRequestLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="specialRequestTxtBox" maxlength="500"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="healthHistoryTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="healthHistoryLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="healthHistoryTxtBox" maxlength="500"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="medicationTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="medicationLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="medicationTxtBox" maxlength="500"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="healingExperienceDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="healingExperienceLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="healingExperienceDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherActivitiesTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherActivitiesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="otherActivitiesTxtBox" maxlength="500"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="therapistFirstViewTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="therapistFirstViewLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="therapistFirstViewTxtBox" maxlength="500"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="appointmentsStartDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="appointmentsStartLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="appointmentsStartDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="appointmentsFrequencyDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="appointmentsFrequencyLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="appointmentsFrequencyDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="interventionModelDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="interventionModelLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="interventionModelDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="lastMedicalCheckupDateTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="lastMedicalCheckupDateLbl"></asp:Label>
                            <div class="col-sm-4">
                                <div class='input-group date' id='lastMedicalCheckupDateDiv'>
                                    <asp:TextBox runat="server" ID="lastMedicalCheckupDateTxtBox" CssClass="form-control"></asp:TextBox>
                                    <span class="input-group-addon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="lastMedicalCheckupTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="lastMedicalCheckupLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input type="text" runat="server" class="form-control" id="lastMedicalCheckupTxtBox" maxlength="50">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div runat="server" class="tab-pane" id="biographicalInfoTab">
            <br />
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="motherCharacteristicsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="motherCharacteristicsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="motherCharacteristicsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherCharacteristicsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherCharacteristicsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="motherInfoTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="motherInfoLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="motherInfoTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherInfoTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherInfoSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherImportantFromMotherFamilyTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherImportantFromMotherFamilyLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="otherImportantFromMotherFamilyDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherImportantFromMotherFamilyTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherImportantFromMotherFamilySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="fatherCharacteristicsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="fatherCharacteristicsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="fatherCharacteristicsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherCharacteristicsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherCharacteristicsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="fatherInfoTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="fatherInfoLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="fatherInfoTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherInfoTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherInfoSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherImportantFromFatherFamilyTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherImportantFromFatherFamilyLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="otherImportantFromFatherFamilyDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherImportantFromFatherFamilyTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherImportantFromFatherFamilySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="motherFamilyHistoryTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="motherFamilyHistoryLbl"></asp:Label>
                            <div class="col-sm-4">
                                <%--<textarea runat="server" class="form-control" id="motherFamilyHistoryTxtBox" maxlength="500"></textarea>--%>
                                <select runat="server" id="motherFamilyHistoryDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherFamilyHistoryTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherFamilyHistorySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="fatherFamilyHistoryTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="fatherFamilyHistoryLbl"></asp:Label>
                            <div class="col-sm-4">
                                <%--<textarea runat="server" class="form-control" id="fatherFamilyHistoryTxtBox" maxlength="500"></textarea>--%>
                                <select runat="server" id="fatherFamilyHistoryDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherFamilyHistoryTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherFamilyHistorySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="familyMedicalHistoryTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="familyMedicalHistoryLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="familyMedicalHistoryTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="familyMedicalHistoryTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="familyMedicalHistorySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherImportantTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherImportantLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="otherImportantDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherImportantTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherImportantSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="motherFeedbackInMySuccessDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="motherFeedbackInMySuccessLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="motherFeedbackInMySuccessDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherFeedbackInMySuccessTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherFeedbackInMySuccessSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="motherFeedbackInMyFailureDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="motherFeedbackInMyFailureLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="motherFeedbackInMyFailureDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherFeedbackInMyFailureTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="motherFeedbackInMyFailureSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="fatherFeedbackInMySuccessDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="fatherFeedbackInMySuccessLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="fatherFeedbackInMySuccessDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherFeedbackInMySuccessTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherFeedbackInMySuccessSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="fatherFeedbackInMyFailureDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="fatherFeedbackInMyFailureLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="fatherFeedbackInMyFailureDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherFeedbackInMyFailureTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="fatherFeedbackInMyFailureSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="importantFeedbackInMySuccessDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="importantFeedbackInMySuccessLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="importantFeedbackInMySuccessDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="importantFeedbackInMySuccessTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="importantFeedbackInMySuccessSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="importantFeedbackInMyFailureDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="importantFeedbackInMyFailureLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="importantFeedbackInMyFailureDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="importantFeedbackInMyFailureTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="importantFeedbackInMyFailureSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="adhesionTypeDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="adhesionTypeLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="adhesionTypeDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="adhesionTypeTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="adhesionTypeSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="preschoolExperiencesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="preschoolExperiencesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="preschoolExperiencesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="preschoolExperiencesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="preschoolExperiencesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="schoolExperiencesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="schoolExperiencesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="schoolExperiencesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schoolExperiencesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schoolExperiencesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="teenageExperiencesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="teenageExperiencesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="teenageExperiencesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="teenageExperiencesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="teenageExperiencesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="adultExperiencesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="adultExperiencesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="adultExperiencesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="adultExperiencesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="adultExperiencesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="workExperiencesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="workExperiencesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="workExperiencesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="workExperiencesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="workExperiencesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="generalTraumaHistoryTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="generalTraumaHistoryTxtLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="generalTraumaHistoryTxtBox" aria-multiline="true" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="generalTraumaHistoryTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="generalTraumaHistorySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="specificTraumaHistoryDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="specificTraumaHistoryLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="specificTraumaHistoryDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="specificTraumaHistoryTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="specificTraumaHistorySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="generalBiographicalInfoTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="generalBiographicalInfoLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="generalBiographicalInfoTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="generalBiographicalInfoTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="generalBiographicalInfoSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div runat="server" class="tab-pane" id="clinicalViewInfoTab">
            <br />
            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="clinicalEvaluationInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="developmentalDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="developmentalLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="developmentalDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="developmentalTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="developmentalSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%-- <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="neurodevelopmentalDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="neurodevelopmentalLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="neurodevelopmentalDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="neurodevelopmentalTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="neurodevelopmentalSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>
            <%-- <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="learningsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="learningsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="learningsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="learningsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="learningsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="emotionalDifficultiesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="emotionalDifficultiesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="emotionalDifficultiesTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="emotionalDifficultiesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="emotionalDifficultiesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="emotionalRemarksTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="emotionalRemarksLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="emotionalRemarksTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="emotionalRemarksTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="emotionalRemarksSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="eatingDisordersInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="eatingDisorderDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="eatingDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="eatingDisorderDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="eatingDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="eatingDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <%--  <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="anorexiaDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="anorexiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="anorexiaDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="anorexiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="anorexiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="bulimiaTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="bulimiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="bulimiaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="bulimiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="bulimiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="overeatingTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="overeatingLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="overeatingTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="overeatingTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="overeatingSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="osfedTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="osfedLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="osfedTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="osfedTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="osfedSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="allophagiaTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="allophagiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="allophagiaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="allophagiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="allophagiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="ruminationDisorderTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="ruminationDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="ruminationDisorderTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="ruminationDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="ruminationDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="foodIntakeDisorderTxtbox" runat="server" class="col-sm-2 control-label" meta:resourcekey="foodIntakeDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="foodIntakeDisorderTxtbox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="foodIntakeDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="foodIntakeDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="orthorexiaTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="orthorexiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="orthorexiaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="orthorexiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="orthorexiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>


            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="moodsInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="moodsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="moodsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="moodsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="moodsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="moodsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%--  <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="majorDepressionDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="majorDepressionLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="majorDepressionDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="majorDepressionTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="majorDepressionSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="cyclothymiaDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="cyclothymiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="cyclothymiaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="cyclothymiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="cyclothymiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="dipolicTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="dipolicLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="dipolicTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="dipolicTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="dipolicSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="dysthymiaDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="dysthymiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="dysthymiaDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="dysthymiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="dysthymiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="melancholicDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="melancholicLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="melancholicChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="melancholicTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="melancholicSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="catatonicChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="catatonicLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="catatonicChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="catatonicTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="catatonicSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="therapyResistantChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="therapyResistantLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="therapyResistantChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="therapyResistantTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="therapyResistantSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="regressionChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="regressionLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="regressionChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="regressionTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="regressionSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="chronicChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="chronicLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="chronicChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="chronicTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="chronicSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="seasonalChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="seasonalLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="seasonalChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="seasonalTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="seasonalSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="epilochiaChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="epilochiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="epilochiaChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="epilochiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="epilochiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="pregnancyChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="pregnancyLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="pregnancyChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="pregnancyTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="pregnancySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="thirdAgeDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="thirdAgeLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="thirdAgeDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="thirdAgeTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="thirdAgeSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="informalDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="informalLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="informalDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="informalTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="informalSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="psychoticDepressionDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="psychoticDepressionLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="psychoticDepressionDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="psychoticDepressionTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="psychoticDepressionSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="elassonDepressiveDisorderChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="elassonDepressiveDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="elassonDepressiveDisorderChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="elassonDepressiveDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="elassonDepressiveDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="shortIntermmitentFormChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="shortIntermmitentFormLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="shortIntermmitentFormChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="shortIntermmitentFormTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="shortIntermmitentFormSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="premenstrualDysphoricDisorderChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="premenstrualDysphoricDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="premenstrualDysphoricDisorderChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="premenstrualDysphoricDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="premenstrualDysphoricDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="depressivePseudonoiaChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="depressivePseudonoiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="depressivePseudonoiaChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="depressivePseudonoiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="depressivePseudonoiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherMoodObservationsTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherMoodObservationsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="otherMoodObservationsTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherMoodObservationsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherMoodObservationsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="anxiousInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="anxietyDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="anxietyLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="anxietyDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="anxietyTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="anxietySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <%--<div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="specificFearsTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="specificFearsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="specificFearsTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="specificFearsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="specificFearsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="socialPhobiaTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="socialPhobiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="socialPhobiaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="socialPhobiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="socialPhobiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="panicDisorderTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="panicDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="panicDisorderTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="panicDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="panicDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="agoraphobiaTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="agoraphobiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="agoraphobiaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="agoraphobiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="agoraphobiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="anxietyPhysicalSymptomsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="anxietyPhysicalSymptomsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="anxietyPhysicalSymptomsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="anxietyPhysicalSymptomsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="anxietyPhysicalSymptomsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="generalizedAxietyChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="generalizedAxietyLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="generalizedAxietyChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="generalizedAxietyTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="generalizedAxietySupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="obsessiveIdeasDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="obsessiveIdeasLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="obsessiveIdeasDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="obsessiveIdeasTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="obsessiveIdeasSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="ideoPsychoComplusionsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="ideoPsychoComplusionsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="ideoPsychoComplusionsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="ideoPsychoComplusionsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="ideoPsychoComplusionsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="tikChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="tikLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="tikChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="tikTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="tikSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="dysmorphobiaDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="dysmorphobiaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="dysmorphobiaDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="dysmorphobiaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="dysmorphobiaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="paracumulationDisorderChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="paracumulationDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="paracumulationDisorderChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="paracumulationDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="paracumulationDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="trichotillomaniaChkBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="trichotillomaniaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <input runat="server" type="checkbox" id="trichotillomaniaChkBox" />
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="trichotillomaniaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="trichotillomaniaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="postTraumaticStressTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="postTraumaticStressLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="postTraumaticStressTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="postTraumaticStressTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="postTraumaticStressSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherStressObservationsTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherStressObservationsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="otherStressObservationsTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherStressObservationsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherStressObservationsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>


            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="sleepInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="sleepDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="sleepLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="sleepDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="sleepTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="sleepSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <%--<div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="insomniaDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="insomniaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="insomniaDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="insomniaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="insomniaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>
            <%-- <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="sleepDisorderDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="sleepDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="sleepDisorderDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="sleepDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="sleepDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>
            <%-- <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherSleepObservationsTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherSleepObservationsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="otherSleepObservationsTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherSleepObservationsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherSleepObservationsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>


            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="psychoticInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="psychoticDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="psychoticLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="psychoticDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="psychoticTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="psychoticSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="defenseMechanismsDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="defenseMechanismsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="defenseMechanismsDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="defenseMechanismsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="defenseMechanismsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="shapesDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="shapesLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="shapesDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="shapesTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="shapesSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <%-- <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="phychoticSymptomsTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="phychoticSymptomsLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="phychoticSymptomsTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="phychoticSymptomsTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="phychoticSymptomsSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="schizophreniaTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="schizophreniaLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="schizophreniaTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizophreniaTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizophreniaSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="schizoemotionalTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="schizoemotionalLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="schizoemotionalTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizoemotionalTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizoemotionalSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="delirusiveTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="delirusiveLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="delirusiveTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="delirusiveTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="delirusiveSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="sortPshychoticTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="sortPshychoticLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="sortPshychoticTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="sortPshychoticTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="sortPshychoticSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="organicPhsychosyndromeTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="organicPhsychosyndromeLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="organicPhsychosyndromeTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="organicPhsychosyndromeTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="organicPhsychosyndromeSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="schizophrenicoformTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="schizophrenicoformLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="schizophrenicoformTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizophrenicoformTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizophrenicoformSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="persistentDisorderTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="persistentDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="persistentDisorderTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="persistentDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="persistentDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="physicalConditionDisorderTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="physicalConditionDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="physicalConditionDisorderTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="physicalConditionDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="physicalConditionDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="nonSpecifiedDisorderTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="nonSpecifiedDisorderLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="nonSpecifiedDisorderTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="nonSpecifiedDisorderTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="nonSpecifiedDisorderSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>--%>


            <div class="row">
                <div class="col-sm-12">
                    <asp:Label runat="server" CssClass="h4" meta:resourcekey="personalityInfoLbl"></asp:Label>
                    <hr class="" />
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="paranoidDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="paranoidLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="paranoidDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="paranoidTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="paranoidSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="schizoidDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="schizoidLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="schizoidDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizoidTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizoidSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="schizotypeDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="schizotypeLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="schizotypeDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizotypeTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="schizotypeSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="onLimitDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="onLimitLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="onLimitDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="onLimitTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="onLimitSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="antisocialDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="antisocialLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="antisocialDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="antisocialTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="antisocialSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="histronicDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="histronicLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="histronicDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="histronicTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="histronicSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="narcissisticDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="narcissisticLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="narcissisticDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="narcissisticTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="narcissisticSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="ideopsychocompressionDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="ideopsychocompressionLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="ideopsychocompressionDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="ideopsychocompressionTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="ideopsychocompressionSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="avoidableDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="avoidableLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="avoidableDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="avoidableTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="avoidableSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="addictiveDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="addictiveLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="addictiveDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="addictiveTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="addictiveSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="passiveAggressiveDDL" runat="server" class="col-sm-2 control-label" meta:resourcekey="passiveAggressiveLbl"></asp:Label>
                            <div class="col-sm-4">
                                <select runat="server" id="passiveAggressiveDDL" datavaluefield="Value" datatextfield="Text" multiple="true" class="form-control no-search-select"></select>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="passiveAggressiveTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="passiveAggressiveSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="otherDisorderInfoTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="otherDisorderInfoLbl"></asp:Label>
                            <div class="col-sm-4">
                                <textarea runat="server" class="form-control" id="otherDisorderInfoTxtBox" maxlength="500"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherDisorderInfoTherapistCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, TherapistCommentsText %>"></textarea>
                            </div>
                            <div class="col-sm-3">
                                <textarea runat="server" class="form-control comment-textarea" id="otherDisorderInfoSupervisorCommentTxtBox" maxlength="500" placeholder="<%$ Resources:GlobalResources, SupervisorCommentsText %>"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div runat="server" class="tab-pane" id="questionnairesTab">
            <br />
            <%--<div class="row">
                <div class="col-xs-12">
                    <div>
                        <div class="form-group">
                            <select runat="server" id="newQuestionnaire2DDL" datavaluefield="Value" datatextfield="Text" class="form-control no-search-select"></select>
                            <asp:LinkButton ID="newQuestionnaireBtn" runat="server" class="btn btn-default btn-flat margin-r-5" CausesValidation="false" OnClick="newQuestionnaireBtn_Click">
                                        <span class="hidden-md hidden-lg"><i class="fa fa-plus"></i></span>
                                        <asp:Label Text="<%$ Resources:GlobalResources, AddText %>" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label>
                            </asp:LinkButton>
                        </div>
                    </div>
                    <hr />
                </div>
            </div>--%>
            <div class="row">
                <div class="col-xs-12">
                    <div class="margin-bottom">
                        <asp:Label ID="questionnairesTitleLbl" runat="server" CssClass="h4" meta:resourcekey="questionnairesTitleLbl"></asp:Label>
                    </div>
                    <%--<div class="row">
                        <div class="col-sm-12">--%>
                    <div class="form-horizontal">
                        <div class="form-group">
                            <asp:Label for="questionnairiesLinkTxtBox" runat="server" class="col-sm-2 control-label" meta:resourcekey="questionnairiesLinkLbl"></asp:Label>
                            <div class="col-sm-10">
                                <div class='input-group'>
                                    <input type="text" runat="server" class="form-control" id="questionnairiesLinkTxtBox" maxlength="500" required>
                                    <span class="input-group-addon">
                                        <a id="A1" runat="server" cssclass="btn btn-default btn-flat" style="cursor: default;" onclick="VisitQuestionnairiesLink(); return false;"><span class="hidden-md hidden-lg"><i class="fa fa-arrow-right"></i></span>
                                            <asp:Label meta:resourcekey="visitQuestionnairiesLinkLbl" CssClass="hidden-sm hidden-xs" runat="server"></asp:Label></a>
                                    </span>
                                </div>
                            </div>

                        </div>
                    </div>
                    <%-- </div>
                    </div>--%>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <%--<asp:UpdatePanel ID="UpdatePanel1" runat="server" EnableViewState="false">
                   <ContentTemplate>--%>
                    <ej:Grid ID="questionnairiesGrid" ClientIDMode="AutoID" runat="server" EnableViewState="true" Locale="el-GR" IsResponsive="true" EnableResponsiveRow="true" EnableRowHover="False" OnServerAddRow="questionnairiesGrid_ServerAddRow" OnServerEditRow="questionnairiesGrid_ServerEditRow" OnServerDeleteRow="questionnairiesGrid_ServerDeleteRow">
                        <SortedColumns>
                            <ej:SortedColumn Field="CreateDate" Direction="Descending" />
                        </SortedColumns>
                        <ToolbarSettings ShowToolbar="true" ToolbarItems="add,edit,delete,update,cancel">
                        </ToolbarSettings>
                        <EditSettings AllowEditing="true" AllowAdding="true" AllowDeleting="true" EditMode="Normal" InlineFormTemplateID="#QuestionnaireTemplate" ShowConfirmDialog="true"></EditSettings>
                        <ClientSideEvents ActionComplete="questionnairiesComplete" BeginEdit="questionnairiesBeforeEdit" />
                        <Columns>
                            <ej:Column Field="Name" HeaderText="Ονομασία" EditType="StringEdit" Width="250">
                                <ValidationRule>
                                    <ej:KeyValue Key="required" Value="true" />
                                    <ej:KeyValue Key="minlength" Value="1" />
                                    <ej:KeyValue Key="maxlength" Value="250" />
                                </ValidationRule>
                            </ej:Column>
                            <ej:Column Field="QuestionnaireCode" EditType="DropdownEdit" HeaderText="Ερωτηματολόγιο" Width="250">
                                <DropDownEditOptions Text="Text" ItemValue="Value" />
                            </ej:Column>
                            <ej:Column Field="CreateDate" Format="{0:dd/MM/yyyy}" HeaderText="Ημ/νία Δημιουργίας" Width="110">
                                <ValidationRule>
                                    <ej:KeyValue Key="required" Value="true" />
                                </ValidationRule>
                            </ej:Column>
                            <ej:Column Field="QuestionnaireInput" HeaderText="Αποτελέσματα Ερωτηματολογίου">
                                <ValidationRule>
                                    <ej:KeyValue Key="maxlength" Value="700" />
                                </ValidationRule>
                                <EditTemplate Create="createTextarea" Read="readTextarea" Write="createTextarea" />
                            </ej:Column>
                            <ej:Column Field="QuestionnaireId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                            </ej:Column>
                        </Columns>
                    </ej:Grid>
                    <%--  </ContentTemplate>
               </asp:UpdatePanel>--%>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-8" style="display: none;">
                    <div>
                        <asp:Label ID="questionnairiesQuestionsTitleLbl" runat="server" CssClass="h4" meta:resourcekey="questionnairiesQuestionsTitleLbl"></asp:Label>
                    </div>

                    <ej:Grid ID="questionnaireQuestionsGrid" runat="server" EnableViewState="true" ClientIDMode="AutoID" AllowSelection="true" Locale="el-GR" IsResponsive="true" EnableResponsiveRow="true" OnServerEditRow="questionnaireQuestionsGrid_ServerEditRow">
                        <ToolbarSettings ShowToolbar="true" ToolbarItems="edit,update,cancel">
                        </ToolbarSettings>
                        <EditSettings AllowEditing="true" AllowAdding="true" AllowDeleting="true" EditMode="Normal" ShowConfirmDialog="true"></EditSettings>
                        <Columns>
                            <ej:Column Field="Title" HeaderText="Τίτλος" AllowEditing="false">
                            </ej:Column>
                            <ej:Column Field="Value" HeaderText="Ποσοστό" Width="120">
                            </ej:Column>
                            <ej:Column Field="Help" HeaderText="Βοήθεια">
                                <ValidationRule>
                                    <ej:KeyValue Key="maxlength" Value="250" />
                                </ValidationRule>
                            </ej:Column>
                            <%--  <ej:Column HeaderText=" " IsUnbound="True" Width="100" AllowResizing="False" AllowTextWrap="False">
                                <Command>
                                    <ej:Commands Type="Edit">
                                        <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                    <ej:Commands Type="Delete">
                                        <ButtonOptions Size="Small" Text="Delete" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-delete" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                </Command>
                            </ej:Column>--%>
                            <ej:Column Field="QuestionnaireQuestionId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                            </ej:Column>
                        </Columns>
                    </ej:Grid>

                </div>
            </div>
        </div>

        <div runat="server" class="tab-pane" id="appointmentsTab">
            <br />
            <div class="row">
                <div class="col-xs-12">
                    <ej:Grid ID="appointmentsGrid" EnableViewState="true" runat="server" AllowSelection="false" IsResponsive="true" OnServerCommandButtonClick="appointmentsGrid_ServerCommandButtonClick" EnableResponsiveRow="true" MinWidth="900">
                        <SortedColumns>
                            <ej:SortedColumn Field="StartTime" Direction="Descending"></ej:SortedColumn>
                        </SortedColumns>
                        <Columns>
                            <ej:Column Field="Recurrence" HeaderText="Επανάληψη" Width="100" TemplateID="#recurrenceColumnTemplate" TextAlign="Center" AllowEditing="false">
                            </ej:Column>
                            <ej:Column Field="StartTime" HeaderText="Ημ/νία" Width="90" Format="{0:dd/MM/yyyy}">
                            </ej:Column>
                            <ej:Column Field="Canceled" HeaderText="Ακυρωμένη" Width="70" Type="checkbox" EditType="BooleanEdit" AllowEditing="false">
                            </ej:Column>
                            <ej:Column Field="ContactFullName" HeaderText="Πελάτης" Width="190">
                            </ej:Column>
                            <ej:Column Field="StartTime" HeaderText="Ώρα Έναρξης" Width="90" Format="{0:HH:mm}">
                            </ej:Column>
                            <ej:Column Field="EndTime" HeaderText="Ώρα Λήξης" Width="90" Format="{0:HH:mm}">
                            </ej:Column>
                            <ej:Column Field="Notes" HeaderText="Σημειώσεις" AllowTextWrap="true" Tooltip="#cellTooltipTemplate" TemplateID="#notesColumnTemplate">
                            </ej:Column>
                            <ej:Column HeaderText=" " IsUnbound="True" Width="65" AllowResizing="False" AllowTextWrap="False">
                                <Command>
                                    <ej:Commands Type="Edit">
                                        <ButtonOptions Size="Small" Text="Edit" Type="Button" ContentType="ImageOnly" PrefixIcon="e-icon e-edit" Width="30"></ButtonOptions>
                                    </ej:Commands>
                                </Command>
                            </ej:Column>
                            <ej:Column Field="AppointmentId" HeaderText="A/A" IsIdentity="True" IsPrimaryKey="True" Visible="False" Width="0">
                            </ej:Column>
                            <%--<ej:Column HeaderText="" Width="40" Template="<span style='height:20px;width:20px;display:block;background-color:{{:AppointmentCategoryColor}}'></span>">
                                    </ej:Column>--%>
                        </Columns>
                        <ClientSideEvents RowDataBound="OnRowDataBound" />
                    </ej:Grid>
                </div>
            </div>
        </div>

        <div runat="server" class="tab-pane" id="aiTab">
            <br />
            <asp:UpdatePanel ID="aiUpdatePanel" runat="server">
                <ContentTemplate>
                    <div class="row ">
                        <div class="col-sm-12 margin-bottom">
                            <table>
                                <tr>
                                    <td>
                                        <asp:LinkButton ID="chatGptDiagnosisAndTherapyBtn" runat="server" CssClass="btn btn-default btn-flat margin-r-5" meta:resourcekey="chatGptDiagnosisAndTherapyBtn" OnClick="chatGptDiagnosisAndTherapyBtn_Click"></asp:LinkButton>
                                    </td>
                                    <td>
                                        <asp:UpdateProgress ID="UpdateProgress1" runat="server" AssociatedUpdatePanelID="aiUpdatePanel">
                                            <ProgressTemplate>
                                                <div class="loading-indicator">
                                                    <img src="content/images/loading.gif" width="30" height="30" alt="Loading..." />
                                                    <span><%= GetGlobalResourceObject("GlobalResources", "InProgress").ToString() %></span>
                                                </div>
                                            </ProgressTemplate>
                                        </asp:UpdateProgress>
                                    </td>
                                </tr>
                            </table>

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <asp:TextBox ID="aiResponseTxtBox" runat="server" TextMode="MultiLine" Width="100%" Height="500px" ReadOnly="true"></asp:TextBox>
                                <span></span>
                                <asp:Label ID="infoLabel" runat="server" Style="white-space: pre-wrap;"></asp:Label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <asp:LinkButton ID="printTextBtn" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" OnClientClick="printDialogText(); return false;">
                                <i class="fa fa-print"></i> <%= GetGlobalResourceObject("GlobalResources", "Print").ToString() %>
                            </asp:LinkButton>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>


        </div>
    </div>


    <asp:HiddenField ID="selectedTab" runat="server" />

    <ej:Dialog ID="contactReportDialog" MinWidth="500" Title="<%$ Resources:GlobalResources, Print %>" runat="server" ShowOnInit="false" IsResponsive="true" EnableResize="false" EnableModal="true">
        <DialogContent>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <asp:Label for="printContactDataChkBox" runat="server" class="col-sm-8 control-label" meta:resourcekey="printContactDataLbl"></asp:Label>
                        <div class="col-sm-4">
                            <ej:CheckBox ID="printContactDataChkBox" runat="server" Size="Medium" Text=""></ej:CheckBox>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <asp:Label for="printContactQuestionnairesChkBox" runat="server" class="col-sm-8 control-label" meta:resourcekey="printContactQuestionnairesLbl"></asp:Label>
                        <div class="col-sm-4">
                            <ej:CheckBox ID="printContactQuestionnairesChkBox" runat="server" Size="Medium" Text=""></ej:CheckBox>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row margin-bottom">
                <div class="col-sm-12">
                    <div class="form-group">
                        <asp:Label for="printContactAppointmentsChkBox" runat="server" class="col-sm-8 control-label" meta:resourcekey="printContactAppointmetsLbl"></asp:Label>
                        <div class="col-sm-4">
                            <ej:CheckBox ID="printContactAppointmentsChkBox" runat="server" Size="Medium" Text=""></ej:CheckBox>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <asp:LinkButton ID="exportContactReportToWordBtn" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" Text="<%$ Resources:GlobalResources, ExportToWord %>" OnClick="exportContactReportToWordBtn_Click"></asp:LinkButton>
                    <asp:LinkButton ID="exportContactReportToPdfBtn" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" Text="<%$ Resources:GlobalResources, ExportToPdf %>" OnClick="exportContactReportToPdfBtn_Click"></asp:LinkButton>
                    <asp:LinkButton ID="previewContactReportBtn" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" Text="<%$ Resources:GlobalResources, Preview %>" OnClick="previewContactReportBtn_Click"></asp:LinkButton>
                </div>
            </div>
        </DialogContent>
    </ej:Dialog>

    <ej:Dialog ID="emailTemplatesDialog" Title="<%$ Resources:GlobalResources, Email %>" runat="server" ShowOnInit="false" IsResponsive="true" EnableResize="false" EnableModal="true">
        <DialogContent>
            <div class="row margin-bottom">
                <div class="col-sm-12">
                    <div class="form-group">
                        <asp:Label for="emailTemplatesLstBox" runat="server" class="col-sm-5 control-label" meta:resourcekey="emailTemplatesLbl"></asp:Label>
                        <div class="col-sm-7">
                            <ej:DropDownList ID="emailTemplatesDDL" DataValueField="EmailTemplateId" DataTextField="Title" runat="server" FilterType="Contains" EnableFilterSearch="true" WatermarkText="" Width="100%">
                            </ej:DropDownList>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <asp:LinkButton ID="sendEmailToContactBtn" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" meta:resourceKey="sendEmailToContactBtn" OnClick="sendEmailToContactBtn_Click"></asp:LinkButton>
                </div>
            </div>
        </DialogContent>
    </ej:Dialog>

    <%-- 
    <ej:Dialog ID="aiDisplayDialog" Title="Text Display" runat="server" ShowOnInit="false" IsResponsive="true" EnableResize="false" EnableModal="true" MinWidth="700" MinHeight="500">
        <DialogContent>
            <div class="row">
                <div class="col-sm-12">
                    <asp:LinkButton ID="cha" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" Text="<%$ Resources:GlobalResources, ExportToWord %>" OnClientClick="AiDiagnosisAndTherapy(); return false;"></asp:LinkButton>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="form-group">
                        <asp:TextBox ID="aiResponseTxtBox" runat="server" TextMode="MultiLine" Width="100%" Height="500px"></asp:TextBox>
                        <asp:Label ID="infoLabel" runat="server" Style="white-space: pre-wrap;"></asp:Label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <asp:LinkButton ID="printTextBtn" runat="server" CssClass="btn btn-default btn-flat pull-right margin-r-5" OnClick="printTextBtn_Click">
                        <i class="fa fa-print"></i> Print
                    </asp:LinkButton>
                </div>
            </div>
        </DialogContent>
    </ej:Dialog>--%>

    <asp:HiddenField runat="server" ID="emailTemplatesDataSourceHiddenFld" />
</asp:Content>
