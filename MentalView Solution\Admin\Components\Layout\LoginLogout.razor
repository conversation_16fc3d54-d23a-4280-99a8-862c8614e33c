@using Microsoft.AspNetCore.Components.Authorization
@using Admin.Services
@using System.Security.Claims

@inject AuthenticationStateProvider AuthStateProvider
@inject IAuthenticationService AuthService
@inject NavigationManager Navigation

<AuthorizeView>
    <Authorized>
        <div class="user-info">
            <span class="user-name">
                <i class="fa-solid fa-user me-1"></i>
                @context.User.Identity?.Name
            </span>
            <button class="btn btn-link logout-btn" @onclick="HandleLogout">
                <i class="fa-solid fa-sign-out-alt me-1"></i>
                Logout
            </button>
        </div>
    </Authorized>
    <NotAuthorized>
        <a href="/login" class="login-link">
            <i class="fa-solid fa-sign-in-alt me-1"></i>
            Login
        </a>
    </NotAuthorized>
</AuthorizeView>

<style>
    .user-info {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #333;
    }

    .user-name {
        font-weight: 500;
        color: #495057;
    }

    .logout-btn {
        color: #dc3545;
        text-decoration: none;
        padding: 0;
        border: none;
        background: none;
        font-size: 14px;
        cursor: pointer;
        transition: color 0.2s ease;
    }

    .logout-btn:hover {
        color: #c82333;
        text-decoration: underline;
    }

    .login-link {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
    }

    .login-link:hover {
        color: #0056b3;
        text-decoration: underline;
    }

    .fa-solid {
        font-size: 14px;
    }
</style>

@code {
    private async Task HandleLogout()
    {
        await AuthService.LogoutAsync();
        Navigation.NavigateTo("/login");
    }
}
