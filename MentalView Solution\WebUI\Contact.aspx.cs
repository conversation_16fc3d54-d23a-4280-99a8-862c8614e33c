﻿using Antlr.Runtime.Misc;
using Data;
using Microsoft.Ajax.Utilities;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Syncfusion.Compression;
using Syncfusion.DocIO;
using Syncfusion.DocIO.DLS;
using Syncfusion.DocToPDFConverter;
using Syncfusion.JavaScript;
using Syncfusion.JavaScript.Models;
using Syncfusion.JavaScript.Web;
using Syncfusion.Linq;
using Syncfusion.Pdf;
using Syncfusion.Windows.Data;
using Syncfusion.XPS;
//using Syncfusion.JavaScript.Models;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Mail;
using System.Reflection;
using System.Reflection.Emit;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Web;
using System.Web.Configuration;
using System.Web.Services;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Xml.Linq;
using static System.Net.Mime.MediaTypeNames;

namespace WebUI
{
    public class Question
    {
        public string Text { get; set; }
        public string Value { get; set; }
    }
    public partial class Contact : System.Web.UI.Page
    {
        List<Question> qcDT = new List<Question>();


        protected void Page_Load(object sender, EventArgs e)
        {
            try
            {

                Data.MentalViewDataSet ds = null;

                ((Main)this.Master).ServerMessage.ButtonClicked += new ButtonClickedHandler(this.ServerMessageButtonClicked);
                ((Main)this.Master).PageTitle = GetLocalResourceObject("Page.Title").ToString();
                //Καλούμε την javascript Initialization() λόγω του UpdatePanel.
                ScriptManager.RegisterStartupScript(this, this.GetType(), "temp", "<script language='javascript'>Initialization();</script>", false);

                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);
                string roleName = userData["Role"].ToString();

                if (roleName == "Guest")
                {
                    this.deleteBtn.Visible = false;
                }

                this.SetDataBindings(tenantId);
                //if (this.onlineIntervationsTreeView.Nodes.Count == 0)
                //{
                //    //Γίνεται χειροκίνητα η εισαγωγή δεδομένων γιατί με databind δεν δουλεύουν τα nodes.
                //    foreach (DataRow dr in Data.FieldValuesMappings.DataSet.Tables["Contacts-OnlineIntervations"].Rows)
                //    {
                //        TreeViewNode node = new TreeViewNode();
                //        node.Text = dr["Text"].ToString();
                //        node.Id = dr["Value"].ToString();
                //        this.onlineIntervationsTreeView.Nodes.Add(node);
                //    }
                //}


                if (!IsPostBack)
                {
                    //Αν δημιουργούμε νέο Contact
                    if (string.IsNullOrEmpty(this.Request.Params["ContactId"]))
                    {


                        ds = new Data.MentalViewDataSet();
                        ds.EnforceConstraints = false;
                        Data.Business.ConfigureDataSet(ref ds);

                        //Δημιουργεί το νεο Contact
                        MentalViewDataSet.ContactsRow contactsRow = ds.Contacts.NewContactsRow();

                        contactsRow.TenantId = tenantId;
                        Guid contactCodeGuid = Guid.NewGuid();
                        contactsRow.ContactCode = Math.Abs(ChecksumCalculator.ChecksumGenerate(contactCodeGuid.ToByteArray(), 0, contactCodeGuid.ToByteArray().Length)).ToString().Substring(0, 6);
                        ds.Contacts.AddContactsRow(contactsRow);

                        ViewState["Contact"] = ds;
                    }
                    else  //Αν κάνουμε επεξεργασία
                    {
                        int contactId = int.Parse(this.Request.Params["ContactId"]);

                        ds = (Data.MentalViewDataSet)Data.Business.ContactsBusiness.GetContactById(contactId);

                        ViewState["Contact"] = ds;
                    }

                    this.SetDataOnUIControls(ds);
                }
                else
                {
                    if (ViewState["Contact"] == null)
                    {
                        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.SessionExpiredMessage, ServerMessageButtons.Ok, "SessionExpired");
                        return;
                    }
                    ds = (MentalViewDataSet)ViewState["Contact"];
                    //this.SetDataOnUIControls(ds);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        public class TextValue
        {
            public string Text { get; set; }
            public string Value { get; set; }
        }

        private void SetDataBindings(long tenantId)
        {
            this.emailTemplatesDDL.DataSource = Data.Business.EmailTemplatesBusiness.GetAllEmailTemplatesList(tenantId).EmailTemplates;
            this.emailTemplatesDDL.DataBind();

            this.emailTemplatesGrid.Columns[0].DataSource = Data.Business.EmailTemplatesBusiness.GetAllEmailTemplatesList(tenantId).EmailTemplates.Select(x => x.Title).ToList();
            this.emailTemplatesGrid.Columns[0].DropDownEditOptions.DataSource = Data.Business.EmailTemplatesBusiness.GetAllEmailTemplatesList(tenantId).EmailTemplates.Select(x => x.Title).ToList();
            this.emailTemplatesGrid.Columns[2].DataSource = new List<string>() { "ΣΕ ΕΞΕΛΙΞΗ", "ΔΙΑΚΟΠΗ", "ΟΛΟΚΛΗΡΩΣΗ" };
            this.emailTemplatesGrid.Columns[2].DropDownEditOptions.DataSource = new List<string>() { "ΣΕ ΕΞΕΛΙΞΗ", "ΔΙΑΚΟΠΗ", "ΟΛΟΚΛΗΡΩΣΗ" };

            DataTable dt;
            DropDownListItem ddlItem;

            #region  AppointmentCategories
            if (this.appointmentCategoryIdDDL.Items.Count == 0)
            {
                this.appointmentCategoryIdDDL.Items.Clear();
                ddlItem = new DropDownListItem();
                ddlItem.Text = "&nbsp;";
                ddlItem.Value = "-1";
                this.appointmentCategoryIdDDL.Items.Add(ddlItem);

                Data.MentalViewDataSet.AppointmentCategoriesDataTable appointmentCategoriesDT = Data.Business.AppointmentCategoriesBusiness.GetAllAppointmentCategories(tenantId);
                //DataTable appointmentCategoriesDT2 = new DataView(appointmentCategoriesDT, "", "CategoryName", DataViewRowState.CurrentRows).ToTable();

                foreach (Data.MentalViewDataSet.AppointmentCategoriesRow appointmentCategoriesRow in appointmentCategoriesDT.Rows)
                {
                    ddlItem = new DropDownListItem();
                    ddlItem.Text = appointmentCategoriesRow.CategoryName;
                    ddlItem.Value = appointmentCategoriesRow.AppointmentCategoryId.ToString();
                    this.appointmentCategoryIdDDL.Items.Add(ddlItem);
                }
            }
            #endregion

            #region  MaritalStatus
            if (this.maritalStatusDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-MaritalStatus"];
                this.maritalStatusDDL.DataSource = dt;
                this.maritalStatusDDL.DataBind();
            }
            #endregion

            #region  LivingStatus
            if (this.livingStatusDDL.Items.Count == 0)
            {
                //this.livingStatusDDL.Items.Clear();

                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-LivingStatus"];
                this.livingStatusDDL.DataSource = dt;
                this.livingStatusDDL.DataBind();

                //foreach (DataRow dr in dt.Rows)
                //{
                //    ddlItem = new DropDownListItem();
                //    ddlItem.Text = dr["Text"].ToString();
                //    ddlItem.Value = dr["Value"].ToString();
                //    this.livingStatusDDL.Items.Add(ddlItem);
                //}
            }
            #endregion

            #region  AccessLevel
            if (this.accessLevelDDL.Items.Count == 0)
            {
                //this.livingStatusDDL.Items.Clear();

                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-AccessLevels"];
                this.accessLevelDDL.DataSource = dt;
                this.accessLevelDDL.DataBind();
            }
            #endregion

            #region  SessionOrigin
            if (this.sessionOriginDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-SessionOrigin"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.sessionOriginDDL.DataSource = dt;
                this.sessionOriginDDL.DataBind();
            }
            #endregion

            #region  State (StateDDL)
            if (this.stateDDL.Items.Count == 0)
            {
                this.stateDDL.Items.Clear();
                ddlItem = new DropDownListItem();
                ddlItem.Text = "&nbsp;";
                ddlItem.Value = "";
                this.stateDDL.Items.Add(ddlItem);

                DataTable statesDT = Data.FieldValuesMappings.DataSet.Tables["Contacts-AppointmentStates"];

                foreach (DataRow statesRow in statesDT.Rows)
                {
                    ddlItem = new DropDownListItem();
                    ddlItem.Text = statesRow["Text"].ToString();
                    ddlItem.Value = statesRow["Value"].ToString();
                    this.stateDDL.Items.Add(ddlItem);
                }
            }
            #endregion

            #region  Sex
            if (this.sexDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Sex"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.sexDDL.DataSource = dt;
                this.sexDDL.DataBind();
            }
            #endregion

            #region  EducationLevel
            if (this.educationLevelDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-EducationLevel"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.educationLevelDDL.DataSource = dt;
                this.educationLevelDDL.DataBind();
            }
            #endregion

            #region  Occupation
            if (this.occupationDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Occupation"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.occupationDDL.DataSource = dt;
                this.occupationDDL.DataBind();
            }
            #endregion

            #region  EconomicStatus
            if (this.economicStatusDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-EconomicStatus"];
                this.economicStatusDDL.DataSource = dt;
                this.economicStatusDDL.DataBind();
            }
            #endregion

            #region  CommunicationMethod
            if (this.communicationMethodDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-CommunicationMethod"];
                this.communicationMethodDDL.DataSource = dt;
                this.communicationMethodDDL.DataBind();
            }
            #endregion

            #region  SessionFrequency
            if (this.sessionFrequencyDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-SessionFrequency"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.sessionFrequencyDDL.DataSource = dt;
                this.sessionFrequencyDDL.DataBind();
            }
            #endregion

            #region  ReferralToAnotherSpecialist
            if (this.referralToAnotherSpecialistDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-ReferralToAnotherSpecialist"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.referralToAnotherSpecialistDDL.DataSource = dt;
                this.referralToAnotherSpecialistDDL.DataBind();
            }
            #endregion

            #region  EyeContact
            if (this.eyeContactDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-EyeContact"];
                this.eyeContactDDL.DataSource = dt;
                this.eyeContactDDL.DataBind();
            }
            #endregion

            #region  BodyLanguage
            if (this.bodyLanguageDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-BodyLanguage"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.bodyLanguageDDL.DataSource = dt;
                this.bodyLanguageDDL.DataBind();
            }
            #endregion

            #region  VoiceTone
            if (this.voiceToneDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-VoiceTone"].Copy();
                this.voiceToneDDL.DataSource = dt;
                this.voiceToneDDL.DataBind();
            }
            #endregion

            #region  Narration
            if (this.narrationDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Narration"].Copy();
                this.narrationDDL.DataSource = dt;
                this.narrationDDL.DataBind();
            }
            #endregion

            #region  SexualOrientation
            if (this.sexualOrientationDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-SexualOrientation"];
                this.sexualOrientationDDL.DataSource = dt;
                this.sexualOrientationDDL.DataBind();
            }
            #endregion

            #region  GeneralRequest
            if (this.generalRequestDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-GeneralRequest"];
                this.generalRequestDDL.DataSource = dt;
                this.generalRequestDDL.DataBind();
            }
            #endregion

            #region  HealingExperience
            if (this.healingExperienceDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-HealingExperience"];
                this.healingExperienceDDL.DataSource = dt;
                this.healingExperienceDDL.DataBind();
            }
            #endregion

            #region  AppointmentsStart
            if (this.appointmentsStartDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-AppointmentsStart"];
                this.appointmentsStartDDL.DataSource = dt;
                this.appointmentsStartDDL.DataBind();
            }
            #endregion

            #region  AppointmentsFrequency
            if (this.appointmentsFrequencyDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-AppointmentsFrequency"];
                this.appointmentsFrequencyDDL.DataSource = dt;
                this.appointmentsFrequencyDDL.DataBind();
            }
            #endregion

            #region  InterventionModel
            if (this.interventionModelDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-InterventionModel"];
                this.interventionModelDDL.DataSource = dt;
                this.interventionModelDDL.DataBind();
            }
            #endregion

            #region  MotherCharacteristics
            if (this.motherCharacteristicsDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-MotherCharacteristics"];
                this.motherCharacteristicsDDL.DataSource = dt;
                this.motherCharacteristicsDDL.DataBind();
            }
            #endregion

            #region  FatherCharacteristics
            if (this.fatherCharacteristicsDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-FatherCharacteristics"];
                this.fatherCharacteristicsDDL.DataSource = dt;
                this.fatherCharacteristicsDDL.DataBind();
            }
            #endregion

            #region  MotherFamilyHistory
            if (this.motherFamilyHistoryDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-MotherFamilyHistory"];
                this.motherFamilyHistoryDDL.DataSource = dt;
                this.motherFamilyHistoryDDL.DataBind();
            }
            #endregion

            #region  OtherImportantFromMotherFamily
            if (this.otherImportantFromMotherFamilyDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-OtherImportantFromMotherFamily"];
                this.otherImportantFromMotherFamilyDDL.DataSource = dt;
                this.otherImportantFromMotherFamilyDDL.DataBind();
            }
            #endregion

            #region  FatherFamilyHistory
            if (this.fatherFamilyHistoryDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-FatherFamilyHistory"];
                this.fatherFamilyHistoryDDL.DataSource = dt;
                this.fatherFamilyHistoryDDL.DataBind();
            }
            #endregion

            #region  OtherImportantFromFatherFamily
            if (this.otherImportantFromFatherFamilyDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-OtherImportantFromFatherFamily"];
                this.otherImportantFromFatherFamilyDDL.DataSource = dt;
                this.otherImportantFromFatherFamilyDDL.DataBind();
            }
            #endregion

            #region  MotherFeedbackInMySuccess
            if (this.motherFeedbackInMySuccessDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-MotherFeedbackInMySuccess"];
                this.motherFeedbackInMySuccessDDL.DataSource = dt;
                this.motherFeedbackInMySuccessDDL.DataBind();
            }
            #endregion

            #region  MotherFeedbackInMyFailure
            if (this.motherFeedbackInMyFailureDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-MotherFeedbackInMyFailure"];
                this.motherFeedbackInMyFailureDDL.DataSource = dt;
                this.motherFeedbackInMyFailureDDL.DataBind();
            }
            #endregion

            #region  FatherFeedbackInMySuccess
            if (this.fatherFeedbackInMySuccessDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-FatherFeedbackInMySuccess"];
                this.fatherFeedbackInMySuccessDDL.DataSource = dt;
                this.fatherFeedbackInMySuccessDDL.DataBind();
            }
            #endregion

            #region  FatherFeedbackInMyFailure
            if (this.fatherFeedbackInMyFailureDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-FatherFeedbackInMyFailure"];
                this.fatherFeedbackInMyFailureDDL.DataSource = dt;
                this.fatherFeedbackInMyFailureDDL.DataBind();
            }
            #endregion

            #region  OtherImportant
            if (this.otherImportantDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-OtherImportant"];
                this.otherImportantDDL.DataSource = dt;
                this.otherImportantDDL.DataBind();
            }
            #endregion

            #region  ImportantFeedbackInMySuccess
            if (this.importantFeedbackInMySuccessDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-ImportantFeedbackInMySuccess"];
                this.importantFeedbackInMySuccessDDL.DataSource = dt;
                this.importantFeedbackInMySuccessDDL.DataBind();
            }
            #endregion

            #region  ImportantFeedbackInMyFailure
            if (this.importantFeedbackInMyFailureDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-ImportantFeedbackInMyFailure"];
                this.importantFeedbackInMyFailureDDL.DataSource = dt;
                this.importantFeedbackInMyFailureDDL.DataBind();
            }
            #endregion

            #region  AdhesionType
            if (this.adhesionTypeDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-AdhesionType"];
                this.adhesionTypeDDL.DataSource = dt;
                this.adhesionTypeDDL.DataBind();
            }
            #endregion

            #region  PreschoolExperiences
            if (this.preschoolExperiencesDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-PreschoolExperiences"];
                this.preschoolExperiencesDDL.DataSource = dt;
                this.preschoolExperiencesDDL.DataBind();
            }
            #endregion

            #region  SchoolExperiences
            if (this.schoolExperiencesDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-SchoolExperiences"];
                this.schoolExperiencesDDL.DataSource = dt;
                this.schoolExperiencesDDL.DataBind();
            }
            #endregion

            #region  TeenageExperiences
            if (this.teenageExperiencesDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-TeenageExperiences"];
                this.teenageExperiencesDDL.DataSource = dt;
                this.teenageExperiencesDDL.DataBind();
            }
            #endregion

            #region  AdultExperiences
            if (this.adultExperiencesDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-AdultExperiences"];
                this.adultExperiencesDDL.DataSource = dt;
                this.adultExperiencesDDL.DataBind();
            }
            #endregion

            #region  WorkExperiences
            if (this.workExperiencesDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-WorkExperiences"];
                this.workExperiencesDDL.DataSource = dt;
                this.workExperiencesDDL.DataBind();
            }
            #endregion

            #region  SpecificTraumaHistory
            if (this.specificTraumaHistoryDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-SpecificTraumaHistory"];
                this.specificTraumaHistoryDDL.DataSource = dt;
                this.specificTraumaHistoryDDL.DataBind();
            }
            #endregion

            #region  Developmental
            if (this.developmentalDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Developmental"];
                this.developmentalDDL.DataSource = dt;
                this.developmentalDDL.DataBind();
            }
            #endregion

            #region  EmotionalProfile
            if (this.emotionalProfileDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-EmotionalProfile"];
                this.emotionalProfileDDL.DataSource = dt;
                this.emotionalProfileDDL.DataBind();
            }
            #endregion

            #region  DefenseMechanisms
            if (this.defenseMechanismsDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-DefenseMechanisms"];
                this.defenseMechanismsDDL.DataSource = dt;
                this.defenseMechanismsDDL.DataBind();
            }
            #endregion

            #region  Shapes
            if (this.shapesDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Shapes"];
                this.shapesDDL.DataSource = dt;
                this.shapesDDL.DataBind();
            }
            #endregion

            //#region  Neurodevelopmental
            //if (this.neurodevelopmentalDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Neurodevelopmental"];
            //    this.neurodevelopmentalDDL.DataSource = dt;
            //    this.neurodevelopmentalDDL.DataBind();
            //}
            //#endregion

            //#region  Learnings
            //if (this.learningsDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Learnings"];
            //    this.learningsDDL.DataSource = dt;
            //    this.learningsDDL.DataBind();
            //}
            //#endregion

            #region  EatingDisorder
            if (this.eatingDisorderDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-EatingDisorder"];
                this.eatingDisorderDDL.DataSource = dt;
                this.eatingDisorderDDL.DataBind();
            }
            #endregion

            //#region  Anorexia
            //if (this.anorexiaDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Anorexia"];
            //    this.anorexiaDDL.DataSource = dt;
            //    this.anorexiaDDL.DataBind();
            //}
            //#endregion

            #region  Moods
            if (this.moodsDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Moods"];
                this.moodsDDL.DataSource = dt;
                this.moodsDDL.DataBind();
            }
            #endregion

            //#region  MajorDepression
            //if (this.majorDepressionDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-MajorDepression"];
            //    this.majorDepressionDDL.DataSource = dt;
            //    this.majorDepressionDDL.DataBind();
            //}
            //#endregion

            //#region  Dysthymia
            //if (this.dysthymiaDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Dysthymia"];
            //    this.dysthymiaDDL.DataSource = dt;
            //    this.dysthymiaDDL.DataBind();
            //}
            //#endregion

            //#region  ThirdAge
            //if (this.thirdAgeDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-ThirdAge"];
            //    this.thirdAgeDDL.DataSource = dt;
            //    this.thirdAgeDDL.DataBind();
            //}
            //#endregion

            //#region  Informal
            //if (this.informalDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Informal"];
            //    this.informalDDL.DataSource = dt;
            //    this.informalDDL.DataBind();
            //}
            //#endregion

            //#region  PsychoticDepression
            //if (this.psychoticDepressionDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-PsychoticDepression"];
            //    this.psychoticDepressionDDL.DataSource = dt;
            //    this.psychoticDepressionDDL.DataBind();
            //}
            //#endregion

            #region  Anxiety
            if (this.anxietyDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Anxiety"];
                this.anxietyDDL.DataSource = dt;
                this.anxietyDDL.DataBind();
            }
            #endregion

            //#region  AnxietyPhysicalSymptoms
            //if (this.anxietyPhysicalSymptomsDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-AnxietyPhysicalSymptoms"];
            //    this.anxietyPhysicalSymptomsDDL.DataSource = dt;
            //    this.anxietyPhysicalSymptomsDDL.DataBind();
            //}
            //#endregion

            //#region  ObsessiveIdeas
            //if (this.obsessiveIdeasDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-ObsessiveIdeas"];
            //    this.obsessiveIdeasDDL.DataSource = dt;
            //    this.obsessiveIdeasDDL.DataBind();
            //}
            //#endregion

            //#region  IdeoPsychoComplusions
            //if (this.ideoPsychoComplusionsDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-IdeoPsychoComplusions"];
            //    this.ideoPsychoComplusionsDDL.DataSource = dt;
            //    this.ideoPsychoComplusionsDDL.DataBind();
            //}
            //#endregion

            //#region  Dysmorphobia
            //if (this.dysmorphobiaDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Dysmorphobia"];
            //    this.dysmorphobiaDDL.DataSource = dt;
            //    this.dysmorphobiaDDL.DataBind();
            //}
            //#endregion

            #region  Sleep
            if (this.sleepDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Sleep"];
                this.sleepDDL.DataSource = dt;
                this.sleepDDL.DataBind();
            }
            #endregion

            //#region  Insomnia
            //if (this.insomniaDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Insomnia"];
            //    this.insomniaDDL.DataSource = dt;
            //    this.insomniaDDL.DataBind();
            //}
            //#endregion

            //#region  SleepDisorder
            //if (this.sleepDisorderDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-SleepDisorder"];
            //    this.sleepDisorderDDL.DataSource = dt;
            //    this.sleepDisorderDDL.DataBind();
            //}
            //#endregion

            #region  Psychotic
            if (this.psychoticDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Psychotic"];
                this.psychoticDDL.DataSource = dt;
                this.psychoticDDL.DataBind();
            }
            #endregion

            #region  Paranoid
            if (this.paranoidDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Paranoid"];
                this.paranoidDDL.DataSource = dt;
                this.paranoidDDL.DataBind();
            }
            #endregion

            #region  Schizoid
            if (this.schizoidDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Schizoid"];
                this.schizoidDDL.DataSource = dt;
                this.schizoidDDL.DataBind();
            }
            #endregion

            #region  Schizotype
            if (this.schizotypeDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Schizotype"];
                this.schizotypeDDL.DataSource = dt;
                this.schizotypeDDL.DataBind();
            }
            #endregion

            #region  OnLimit
            if (this.onLimitDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-OnLimit"];
                this.onLimitDDL.DataSource = dt;
                this.onLimitDDL.DataBind();
            }
            #endregion

            #region Antisocial
            if (this.antisocialDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Antisocial"];
                this.antisocialDDL.DataSource = dt;
                this.antisocialDDL.DataBind();
            }
            #endregion

            #region Histronic
            if (this.histronicDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Histronic"];
                this.histronicDDL.DataSource = dt;
                this.histronicDDL.DataBind();
            }
            #endregion

            #region Narcissistic
            if (this.narcissisticDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Narcissistic"];
                this.narcissisticDDL.DataSource = dt;
                this.narcissisticDDL.DataBind();
            }
            #endregion

            #region Ideopsychocompression
            if (this.ideopsychocompressionDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Ideopsychocompression"];
                this.ideopsychocompressionDDL.DataSource = dt;
                this.ideopsychocompressionDDL.DataBind();
            }
            #endregion

            #region Avoidable
            if (this.avoidableDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Avoidable"];
                this.avoidableDDL.DataSource = dt;
                this.avoidableDDL.DataBind();
            }
            #endregion

            #region Addictive
            if (this.addictiveDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-Addictive"];
                this.addictiveDDL.DataSource = dt;
                this.addictiveDDL.DataBind();
            }
            #endregion

            #region PassiveAggressive
            if (this.passiveAggressiveDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-PassiveAggressive"];
                this.passiveAggressiveDDL.DataSource = dt;
                this.passiveAggressiveDDL.DataBind();
            }
            #endregion

            #region  InactiveReason
            if (this.inactiveReasonDDL.Items.Count == 0)
            {
                dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-InactiveReason"].Copy();
                DataRow emptyRow = dt.NewRow();
                emptyRow["Text"] = "\xA0";
                emptyRow["Value"] = "";
                dt.Rows.InsertAt(emptyRow, 0);
                this.inactiveReasonDDL.DataSource = dt;
                this.inactiveReasonDDL.DataBind();
            }



            //if (this.questionnaireCodeDDL.Items.Count == 0)
            //{
            //    dt = Data.FieldValuesMappings.DataSet.Tables["Contacts-InactiveReason"].Copy();
            //    DataRow emptyRow = dt.NewRow();
            //    emptyRow["Text"] = "\xA0";
            //    emptyRow["Value"] = "";
            //    dt.Rows.InsertAt(emptyRow, 0);
            //    this.questionnaireCodeDDL.DataSource = dt;
            //    this.questionnaireCodeDDL.DataBind();
            //}

            #endregion

            #region  QuestionnairesInGrid
            DataTable questionnairesInGridDT = new DataTable();
            if (questionnairesInGridDT.Rows.Count == 0)
            {
                questionnairesInGridDT = Data.FieldValuesMappings.DataSet.Tables["Contacts-Questionnaires"];

            }
            #endregion

            MentalViewDataSet doctorsDS = Data.Business.UsersBusiness.GetAllDoctorUsersList(tenantId);
            MentalViewDataSet.UsersRow emptyDoctorsRow = doctorsDS.Users.NewUsersRow();
            emptyDoctorsRow.FullName = "\xA0";
            doctorsDS.Users.Rows.InsertAt(emptyDoctorsRow, 0);

            //TherapistId
            this.therapistIdDDL.DataSource = doctorsDS.Users;
            this.therapistIdDDL.DataBind();

            //CoTherapistId
            this.coTherapistIdDDL.DataSource = doctorsDS.Users;
            this.coTherapistIdDDL.DataBind();

            //DiagnosticianId
            this.diagnosticianIdDDL.DataSource = doctorsDS.Users;
            this.diagnosticianIdDDL.DataBind();

            //ClinicSupervisor
            this.clinicSupervisorIdDDL.DataSource = doctorsDS.Users;
            this.clinicSupervisorIdDDL.DataBind();

            //GuestId
            MentalViewDataSet guestsDS = Data.Business.UsersBusiness.GetAllGuestUsers(tenantId);
            MentalViewDataSet.UsersRow emptyGuestsRow = guestsDS.Users.NewUsersRow();
            emptyGuestsRow.FullName = "\xA0";
            guestsDS.Users.Rows.InsertAt(emptyGuestsRow, 0);
            this.guestIdDDL.DataSource = guestsDS.Users;
            this.guestIdDDL.DataBind();

            DataTable questionnairesDT = new DataTable();
            questionnairesDT.Columns.Add("Text", typeof(string));
            questionnairesDT.Columns.Add("Value", typeof(string));
            questionnairesDT.Rows.Add(new string[] { "\xA0", "" });
            questionnairesDT.Rows.Add(new string[] { "Ποιότητα Ζωής", "LifeQuality" });
            questionnairesDT.Rows.Add(new string[] { "Άγχος Κατάθλιψη Πανικός", "AnxietyDepressionPanic" });
            questionnairesDT.Rows.Add(new string[] { "Συναισθηματική Αναπτ. Συναίσθημα", "EmotionalDevelopment" });
            questionnairesDT.Rows.Add(new string[] { "Ιστορικού", "Background" });
            questionnairesDT.Rows.Add(new string[] { "Σχημάτων", "Shapes" });
            questionnairesDT.Rows.Add(new string[] { "Μηχανισμοί Άμυνας", "DefenceMechanisms" });
            questionnairesDT.Rows.Add(new string[] { "Προσωπικότητα Εννέα", "PersonalityNine" });
            questionnairesDT.Rows.Add(new string[] { "Ψ/Παθολογίας Εναλλαγές Διάθεσης", "P-PathologicalMoodChanges" });
            questionnairesDT.Rows.Add(new string[] { "Ψ/Παθολογίας Προσωπικότητα", "P-PathologicalPersonnality" });
            //this.newQuestionnaire2DDL.DataSource = questionnairesDT;
            //this.newQuestionnaire2DDL.DataBind();
        }

        private void SetDataOnUIControls(Data.MentalViewDataSet ds)
        {
            try
            {

                //foreach (DataColumn column in ds.Contacts.Columns)
                //{
                //    if (column.DataType == typeof(string))
                //    {
                //        if (ds.Contacts[0][column].ToString()== "")
                //        {
                //            ds.Contacts[0][column] = " ";
                //        }
                //    }
                //}

                this.firstNameTxtBox.Value = ds.Contacts[0].FirstName;
                this.lastNameTxtBox.Value = ds.Contacts[0].LastName;
                this.contactCodeTxtBox.Value = ds.Contacts[0].ContactCode;
                this.stateDDL.Value = ds.Contacts[0].AppointmentsState;
                if (ds.Contacts[0].IsAppointmentCategoryIdNull() == false)
                {
                    this.appointmentCategoryIdDDL.Value = ds.Contacts[0].AppointmentCategoryId.ToString();
                }
                this.activeChkBox.Checked = ds.Contacts[0].Active;
                this.SetValueOnSelectControl(this.inactiveReasonDDL, ds.Contacts[0].InactiveReason);
                this.waitingChkBox.Checked = ds.Contacts[0].Waiting;
                this.therapistIdDDL.Value = ds.Contacts[0].IsTherapistIdNull() ? "" : ds.Contacts[0].TherapistId.ToString();
                this.coTherapistIdDDL.Value = ds.Contacts[0].IsCoTherapistIdNull() ? "" : ds.Contacts[0].CoTherapistId.ToString();
                this.guestIdDDL.Value = ds.Contacts[0].IsGuestIdNull() ? "" : ds.Contacts[0].GuestId.ToString();
                this.diagnosticianIdDDL.Value = ds.Contacts[0].IsDiagnosticianIdNull() ? "" : ds.Contacts[0].DiagnosticianId.ToString();
                this.clinicSupervisorIdDDL.Value = ds.Contacts[0].IsClinicSupervisorIdNull() ? "" : ds.Contacts[0].ClinicSupervisorId.ToString();
                if (ds.Contacts[0].IsPsychotherapyStartDateNull() == false)
                {
                    this.psychotherapyStartDateTextBox.Text = ds.Contacts[0].PsychotherapyStartDate.ToString(WebConfigurationManager.AppSettings["DateFormat"]);
                }
                if (ds.Contacts[0].IsBirthDateNull() == false)
                {
                    this.birthDateTxtBox.Text = ds.Contacts[0].BirthDate.ToString(WebConfigurationManager.AppSettings["DateFormat"]);
                }
                this.SetValueOnSelectControl(this.sessionOriginDDL, ds.Contacts[0].SessionOrigin);
                this.SetValueOnSelectControl(this.sexDDL, ds.Contacts[0].Sex);
                this.phone1TxtBox.Value = ds.Contacts[0].Phone1;
                this.mobile1TxtBox.Value = ds.Contacts[0].Mobile1;
                this.emergencyContactNameTxtBox.Value = ds.Contacts[0].EmergencyContactName;
                this.emergencyPhoneTxtBox.Value = ds.Contacts[0].EmergencyPhone;
                this.email1TxtBox.Value = ds.Contacts[0].Email;
                this.email2TxtBox.Value = ds.Contacts[0].Email2;
                this.birthPlaceTxtBox.Value = ds.Contacts[0].BirthPlace;
                this.residenceTxtBox.Value = ds.Contacts[0].Residence;
                this.SetValueOnSelectControl(this.educationLevelDDL, ds.Contacts[0].EducationLevel);
                this.SetValueOnSelectControl(this.maritalStatusDDL, ds.Contacts[0].MaritalStatus);
                this.childrenTxtBox.Value = ds.Contacts[0].Children;
                this.childrenAgeTxtBox.Value = ds.Contacts[0].ChildrenAge;
                this.SetValueOnSelectControl(this.livingStatusDDL, ds.Contacts[0].LivingStatus);
                this.SetValueOnSelectControl(this.occupationDDL, ds.Contacts[0].Occupation);
                this.SetValueOnSelectControl(this.economicStatusDDL, ds.Contacts[0].EconomicStatus);
                this.SetValueOnSelectControl(this.communicationMethodDDL, ds.Contacts[0].CommunicationMethod);
                this.SetValueOnSelectControl(this.sessionFrequencyDDL, ds.Contacts[0].SessionFrequency);
                this.SetValueOnSelectControl(this.referralToAnotherSpecialistDDL, ds.Contacts[0].ReferralToAnotherSpecialist);
                this.notesTxtBox.Text = ds.Contacts[0].Notes;
                this.caseFormulationTxtBox.Text = ds.Contacts[0].CaseFormulation;
                this.specilistObservationsTxtBox.Text = ds.Contacts[0].SpecialistObservations;
                this.SetValueOnSelectControl(this.accessLevelDDL, ds.Contacts[0].AccessLevel);
                this.questionnairiesLinkTxtBox.Value = ds.Contacts[0].QuestionnairiesLink;
                this.skillsTxtBox.Value = ds.Contacts[0].Skills;
                this.traumaticHistoryTxtBox.Value = ds.Contacts[0].TraumaticHistory;
                this.negativeBeliefsTxtBox.Value = ds.Contacts[0].NegativeBeliefs;
                this.negativeEmotionsTxtBox.Value = ds.Contacts[0].NegativeEmotions;
                this.triggeringEventsTxtBox.Value = ds.Contacts[0].TriggeringEvents;
                this.dysfunctionalBehaviorsTxtBox.Value = ds.Contacts[0].DysfunctionalBehaviors;
                this.secondaryBenefitTxtBox.Value = ds.Contacts[0].SecondaryBenefit;
                this.SetValueOnSelectControl(this.emotionalProfileDDL, ds.Contacts[0].EmotionalProfile);


                //ContactEmailTemplates
                this.emailTemplatesGrid.DataSource = ds.ContactEmailTemplates;
                this.emailTemplatesGrid.DataBind();
                //Αν η τιμή που έχει κάποιο ContactEmailTemplate δεν υπάρχει στην λίστα (γιατί μπορεί να έχει αλλάξει ονομασία ή έχει διαγραφεί) το προσθέτει για να μην χαθεί η τιμή του.
                foreach (MentalViewDataSet.ContactEmailTemplatesRow row in ds.ContactEmailTemplates)
                {
                    if (((List<string>)this.emailTemplatesGrid.Columns[0].DropDownEditOptions.DataSource).Contains(row.EmailTemplateTitle) == false)
                    {
                        ((List<string>)this.emailTemplatesGrid.Columns[0].DropDownEditOptions.DataSource).Add(row.EmailTemplateTitle);
                    }
                }

                //if (ds.Contacts[0].OnlineIntervations != null || ds.Contacts[0].OnlineIntervations != "")
                //{
                //    string[] vals = ds.Contacts[0].OnlineIntervations.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
                //    //OnlineIntervations listview
                //    foreach (string onlineIntervationValue in vals)
                //    {
                //        if (this.onlineIntervationsTreeView.Nodes.Find(x => x.Id == onlineIntervationValue) != null)
                //        {
                //            this.onlineIntervationsTreeView.Nodes.Find(x => x.Id == onlineIntervationValue).Checked = true;
                //        }
                //    }
                //}

                //Tab Personal & Medical Info
                this.SetValueOnSelectControl(this.eyeContactDDL, ds.Contacts[0].EyeContact);
                this.SetValueOnSelectControl(this.bodyLanguageDDL, ds.Contacts[0].BodyLanguage);
                this.SetValueOnSelectControl(this.voiceToneDDL, ds.Contacts[0].VoiceTone);
                this.SetValueOnSelectControl(this.narrationDDL, ds.Contacts[0].Narration);
                this.SetValueOnSelectControl(this.sexualOrientationDDL, ds.Contacts[0].SexualOrientation);
                this.SetValueOnSelectControl(this.generalRequestDDL, ds.Contacts[0].GeneralRequest);
                this.specialRequestTxtBox.Value = ds.Contacts[0].SpecialRequest;
                this.healthHistoryTxtBox.Value = ds.Contacts[0].HealthHistory;
                this.medicationTxtBox.Value = ds.Contacts[0].Medication;
                this.SetValueOnSelectControl(this.healingExperienceDDL, ds.Contacts[0].HealingExperience);
                this.otherActivitiesTxtBox.Value = ds.Contacts[0].OtherActivities;
                this.therapistFirstViewTxtBox.Value = ds.Contacts[0].TherapistFirstView;
                this.SetValueOnSelectControl(this.appointmentsStartDDL, ds.Contacts[0].AppointmentsStart);
                this.SetValueOnSelectControl(this.appointmentsFrequencyDDL, ds.Contacts[0].AppointmentsFrequency);
                this.SetValueOnSelectControl(this.interventionModelDDL, ds.Contacts[0].InterventionModel);
                if (ds.Contacts[0].IsLastMedicalCheckupDateNull() == false)
                {
                    this.lastMedicalCheckupDateTxtBox.Text = ds.Contacts[0].LastMedicalCheckupDate.ToString(WebConfigurationManager.AppSettings["DateFormat"]);
                }
                this.lastMedicalCheckupTxtBox.Value = ds.Contacts[0].LastMedicalCheckup;

                //Tab Biographical Info
                this.SetValueOnSelectControl(this.motherCharacteristicsDDL, ds.Contacts[0].MotherCharacteristics);
                this.motherCharacteristicsTherapistCommentTxtBox.Value = ds.Contacts[0].MotherCharacteristicsTherapistComment;
                this.motherCharacteristicsSupervisorCommentTxtBox.Value = ds.Contacts[0].MotherCharacteristicsSupervisorComment;
                this.SetValueOnSelectControl(this.fatherCharacteristicsDDL, ds.Contacts[0].FatherCharacteristics);
                this.fatherCharacteristicsTherapistCommentTxtBox.Value = ds.Contacts[0].FatherCharacteristicsTherapistComment;
                this.fatherCharacteristicsSupervisorCommentTxtBox.Value = ds.Contacts[0].FatherCharacteristicsSupervisorComment;
                this.motherInfoTxtBox.Value = ds.Contacts[0].MotherInfo;
                this.motherInfoTherapistCommentTxtBox.Value = ds.Contacts[0].MotherInfoTherapistComment;
                this.motherInfoSupervisorCommentTxtBox.Value = ds.Contacts[0].MotherInfoSupervisorComment;
                this.fatherInfoTxtBox.Value = ds.Contacts[0].FatherInfo;
                this.fatherInfoTherapistCommentTxtBox.Value = ds.Contacts[0].FatherInfoTherapistComment;
                this.fatherInfoSupervisorCommentTxtBox.Value = ds.Contacts[0].FatherInfoSupervisorComment;
                this.SetValueOnSelectControl(this.otherImportantFromMotherFamilyDDL, ds.Contacts[0].OtherImportantFromMotherFamily);
                this.otherImportantFromMotherFamilyTherapistCommentTxtBox.Value = ds.Contacts[0].OtherImportantFromMotherFamilyTherapistComment;
                this.otherImportantFromMotherFamilySupervisorCommentTxtBox.Value = ds.Contacts[0].OtherImportantFromMotherFamilySupervisorComment;
                this.SetValueOnSelectControl(this.otherImportantFromFatherFamilyDDL, ds.Contacts[0].OtherImportantFromFatherFamily);
                this.otherImportantFromFatherFamilyTherapistCommentTxtBox.Value = ds.Contacts[0].OtherImportantFromFatherFamilyTherapistComment;
                this.otherImportantFromFatherFamilySupervisorCommentTxtBox.Value = ds.Contacts[0].OtherImportantFromFatherFamilySupervisorComment;
                this.SetValueOnSelectControl(this.motherFamilyHistoryDDL, ds.Contacts[0].MotherFamilyHistory);
                this.motherFamilyHistoryTherapistCommentTxtBox.Value = ds.Contacts[0].MotherFamilyHistoryTherapistComment;
                this.motherFamilyHistorySupervisorCommentTxtBox.Value = ds.Contacts[0].MotherFamilyHistorySupervisorComment;
                this.SetValueOnSelectControl(this.fatherFamilyHistoryDDL, ds.Contacts[0].FatherFamilyHistory);
                this.fatherFamilyHistoryTherapistCommentTxtBox.Value = ds.Contacts[0].FatherFamilyHistoryTherapistComment;
                this.fatherFamilyHistorySupervisorCommentTxtBox.Value = ds.Contacts[0].FatherFamilyHistorySupervisorComment;
                this.familyMedicalHistoryTxtBox.Value = ds.Contacts[0].FamilyMedicalHistory;
                this.familyMedicalHistoryTherapistCommentTxtBox.Value = ds.Contacts[0].FamilyMedicalHistoryTherapistComment;
                this.familyMedicalHistorySupervisorCommentTxtBox.Value = ds.Contacts[0].FamilyMedicalHistorySupervisorComment;
                this.SetValueOnSelectControl(this.otherImportantDDL, ds.Contacts[0].OtherImportant);
                this.otherImportantTherapistCommentTxtBox.Value = ds.Contacts[0].OtherImportantTherapistComment;
                this.otherImportantSupervisorCommentTxtBox.Value = ds.Contacts[0].OtherImportantSupervisorComment;
                this.SetValueOnSelectControl(this.motherFeedbackInMySuccessDDL, ds.Contacts[0].MotherFeedbackInMySuccess);
                this.motherFeedbackInMySuccessTherapistCommentTxtBox.Value = ds.Contacts[0].MotherFeedbackInMySuccessTherapistComment;
                this.motherFeedbackInMySuccessSupervisorCommentTxtBox.Value = ds.Contacts[0].MotherFeedbackInMySuccessSupervisorComment;
                this.SetValueOnSelectControl(this.motherFeedbackInMyFailureDDL, ds.Contacts[0].MotherFeedbackInMyFailure);
                this.motherFeedbackInMyFailureTherapistCommentTxtBox.Value = ds.Contacts[0].MotherFeedbackInMyFailureTherapistComment;
                this.motherFeedbackInMyFailureSupervisorCommentTxtBox.Value = ds.Contacts[0].MotherFeedbackInMyFailureSupervisorComment;
                this.SetValueOnSelectControl(this.fatherFeedbackInMySuccessDDL, ds.Contacts[0].FatherFeedbackInMySuccess);
                this.fatherFeedbackInMySuccessTherapistCommentTxtBox.Value = ds.Contacts[0].FatherFeedbackInMySuccessTherapistComment;
                this.fatherFeedbackInMySuccessSupervisorCommentTxtBox.Value = ds.Contacts[0].FatherFeedbackInMySuccessSupervisorComment;
                this.SetValueOnSelectControl(this.fatherFeedbackInMyFailureDDL, ds.Contacts[0].FatherFeedbackInMyFailure);
                this.fatherFeedbackInMyFailureTherapistCommentTxtBox.Value = ds.Contacts[0].FatherFeedbackInMyFailureTherapistComment;
                this.fatherFeedbackInMyFailureSupervisorCommentTxtBox.Value = ds.Contacts[0].FatherFeedbackInMyFailureSupervisorComment;
                this.SetValueOnSelectControl(this.importantFeedbackInMySuccessDDL, ds.Contacts[0].ImportantFeedbackInMySuccess);
                this.importantFeedbackInMySuccessTherapistCommentTxtBox.Value = ds.Contacts[0].ImportantFeedbackInMySuccessTherapistComment;
                this.importantFeedbackInMySuccessSupervisorCommentTxtBox.Value = ds.Contacts[0].ImportantFeedbackInMySuccessSupervisorComment;
                this.SetValueOnSelectControl(this.importantFeedbackInMyFailureDDL, ds.Contacts[0].ImportantFeedbackInMyFailure);
                this.importantFeedbackInMyFailureTherapistCommentTxtBox.Value = ds.Contacts[0].ImportantFeedbackInMyFailureTherapistComment;
                this.importantFeedbackInMyFailureSupervisorCommentTxtBox.Value = ds.Contacts[0].ImportantFeedbackInMyFailureSupervisorComment;
                this.SetValueOnSelectControl(this.adhesionTypeDDL, ds.Contacts[0].AdhesionType);
                this.adhesionTypeTherapistCommentTxtBox.Value = ds.Contacts[0].AdhesionTypeTherapistComment;
                this.adhesionTypeSupervisorCommentTxtBox.Value = ds.Contacts[0].AdhesionTypeSupervisorComment;
                this.SetValueOnSelectControl(this.preschoolExperiencesDDL, ds.Contacts[0].PreschoolExperiences);
                this.preschoolExperiencesTherapistCommentTxtBox.Value = ds.Contacts[0].PreschoolExperiencesTherapistComment;
                this.preschoolExperiencesSupervisorCommentTxtBox.Value = ds.Contacts[0].PreschoolExperiencesSupervisorComment;
                this.SetValueOnSelectControl(this.schoolExperiencesDDL, ds.Contacts[0].SchoolExperiences);
                this.schoolExperiencesTherapistCommentTxtBox.Value = ds.Contacts[0].SchoolExperiencesTherapistComment;
                this.schoolExperiencesSupervisorCommentTxtBox.Value = ds.Contacts[0].SchoolExperiencesSupervisorComment;
                this.SetValueOnSelectControl(this.teenageExperiencesDDL, ds.Contacts[0].TeenageExperiences);
                this.teenageExperiencesTherapistCommentTxtBox.Value = ds.Contacts[0].TeenageExperiencesTherapistComment;
                this.teenageExperiencesSupervisorCommentTxtBox.Value = ds.Contacts[0].TeenageExperiencesSupervisorComment;
                this.SetValueOnSelectControl(this.adultExperiencesDDL, ds.Contacts[0].AdultExperiences);
                this.adultExperiencesTherapistCommentTxtBox.Value = ds.Contacts[0].AdultExperiencesTherapistComment;
                this.adultExperiencesSupervisorCommentTxtBox.Value = ds.Contacts[0].AdultExperiencesSupervisorComment;
                this.SetValueOnSelectControl(this.workExperiencesDDL, ds.Contacts[0].WorkExperiences);
                this.workExperiencesTherapistCommentTxtBox.Value = ds.Contacts[0].WorkExperiencesTherapistComment;
                this.workExperiencesSupervisorCommentTxtBox.Value = ds.Contacts[0].WorkExperiencesSupervisorComment;
                this.generalTraumaHistoryTxtBox.Value = ds.Contacts[0].GeneralTraumaHistory;
                this.generalTraumaHistoryTherapistCommentTxtBox.Value = ds.Contacts[0].GeneralTraumaHistoryTherapistComment;
                this.generalTraumaHistorySupervisorCommentTxtBox.Value = ds.Contacts[0].GeneralTraumaHistorySupervisorComment;
                this.SetValueOnSelectControl(this.specificTraumaHistoryDDL, ds.Contacts[0].SpecificTraumaHistory);
                this.specificTraumaHistoryTherapistCommentTxtBox.Value = ds.Contacts[0].SpecificTraumaHistoryTherapistComment;
                this.specificTraumaHistorySupervisorCommentTxtBox.Value = ds.Contacts[0].SpecificTraumaHistorySupervisorComment;
                this.generalBiographicalInfoTxtBox.Value = ds.Contacts[0].GeneralBiographicalInfo;
                this.generalBiographicalInfoTherapistCommentTxtBox.Value = ds.Contacts[0].GeneralBiographicalInfoTherapistComment;
                this.generalBiographicalInfoSupervisorCommentTxtBox.Value = ds.Contacts[0].GeneralBiographicalInfoSupervisorComment;


                //Tab ClinicalView
                this.SetValueOnSelectControl(this.developmentalDDL, ds.Contacts[0].Developmental);
                this.developmentalTherapistCommentTxtBox.Value = ds.Contacts[0].DevelopmentalTherapistComment;
                this.developmentalSupervisorCommentTxtBox.Value = ds.Contacts[0].DevelopmentalSupervisorComment;
                //this.SetValueOnSelectControl(this.neurodevelopmentalDDL, ds.Contacts[0].Neurodevelopmental);
                //this.neurodevelopmentalTherapistCommentTxtBox.Value = ds.Contacts[0].NeurodevelopmentalTherapistComment;
                //this.neurodevelopmentalSupervisorCommentTxtBox.Value = ds.Contacts[0].NeurodevelopmentalSupervisorComment;
                //this.SetValueOnSelectControl(this.learningsDDL, ds.Contacts[0].Learnings);
                //this.learningsTherapistCommentTxtBox.Value = ds.Contacts[0].LearningsTherapistComment;
                //this.learningsSupervisorCommentTxtBox.Value = ds.Contacts[0].LearningsSupervisorComment;
                this.emotionalDifficultiesTxtBox.Value = ds.Contacts[0].EmotionalDifficulties;
                this.emotionalDifficultiesTherapistCommentTxtBox.Value = ds.Contacts[0].EmotionalDifficultiesTherapistComment;
                this.emotionalDifficultiesSupervisorCommentTxtBox.Value = ds.Contacts[0].EmotionalDifficultiesSupervisorComment;
                this.emotionalRemarksTxtBox.Value = ds.Contacts[0].EmotionalRemarks;
                this.emotionalRemarksTherapistCommentTxtBox.Value = ds.Contacts[0].EmotionalRemarksTherapistComment;
                this.emotionalRemarksSupervisorCommentTxtBox.Value = ds.Contacts[0].EmotionalRemarksSupervisorComment;
                this.SetValueOnSelectControl(this.eatingDisorderDDL, ds.Contacts[0].EatingDisorder);
                this.eatingDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].EatingDisorderTherapistComment;
                this.eatingDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].EatingDisorderSupervisorComment;
                //this.SetValueOnSelectControl(this.anorexiaDDL, ds.Contacts[0].Anorexia);
                //this.anorexiaTherapistCommentTxtBox.Value = ds.Contacts[0].AnorexiaTherapistComment;
                //this.anorexiaSupervisorCommentTxtBox.Value = ds.Contacts[0].AnorexiaSupervisorComment;
                //this.bulimiaTxtBox.Value = ds.Contacts[0].Bulimia;
                //this.bulimiaTherapistCommentTxtBox.Value = ds.Contacts[0].BulimiaTherapistComment;
                //this.bulimiaSupervisorCommentTxtBox.Value = ds.Contacts[0].BulimiaSupervisorComment;
                //this.overeatingTxtBox.Value = ds.Contacts[0].Overeating;
                //this.overeatingTherapistCommentTxtBox.Value = ds.Contacts[0].OvereatingTherapistComment;
                //this.overeatingSupervisorCommentTxtBox.Value = ds.Contacts[0].OvereatingSupervisorComment;
                //this.osfedTxtBox.Value = ds.Contacts[0].Osfed;
                //this.osfedTherapistCommentTxtBox.Value = ds.Contacts[0].OsfedTherapistComment;
                //this.osfedSupervisorCommentTxtBox.Value = ds.Contacts[0].OsfedSupervisorComment;
                //this.allophagiaTxtBox.Value = ds.Contacts[0].Allophagia;
                //this.allophagiaTherapistCommentTxtBox.Value = ds.Contacts[0].AllophagiaTherapistComment;
                //this.allophagiaSupervisorCommentTxtBox.Value = ds.Contacts[0].AllophagiaSupervisorComment;
                //this.ruminationDisorderTxtBox.Value = ds.Contacts[0].RuminationDisorder;
                //this.ruminationDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].RuminationDisorderTherapistComment;
                //this.ruminationDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].RuminationDisorderSupervisorComment;
                //this.foodIntakeDisorderTxtbox.Value = ds.Contacts[0].FoodIntakeDisorder;
                //this.foodIntakeDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].FoodIntakeDisorderTherapistComment;
                //this.foodIntakeDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].FoodIntakeDisorderSupervisorComment;
                //this.orthorexiaTxtBox.Value = ds.Contacts[0].Orthorexia;
                //this.orthorexiaTherapistCommentTxtBox.Value = ds.Contacts[0].OrthorexiaTherapistComment;
                //this.orthorexiaSupervisorCommentTxtBox.Value = ds.Contacts[0].OrthorexiaSupervisorComment;

                this.SetValueOnSelectControl(this.moodsDDL, ds.Contacts[0].Moods);
                this.moodsTherapistCommentTxtBox.Value = ds.Contacts[0].MoodsTherapistComment;
                this.moodsSupervisorCommentTxtBox.Value = ds.Contacts[0].MoodsSupervisorComment;

                //this.SetValueOnSelectControl(this.majorDepressionDDL, ds.Contacts[0].MajorDepression);
                //this.majorDepressionTherapistCommentTxtBox.Value = ds.Contacts[0].MajorDepressionTherapistComment;
                //this.majorDepressionSupervisorCommentTxtBox.Value = ds.Contacts[0].MajorDepressionSupervisorComment;
                //this.cyclothymiaTxtBox.Value = ds.Contacts[0].Cyclothymia;
                //this.cyclothymiaTherapistCommentTxtBox.Value = ds.Contacts[0].CyclothymiaTherapistComment;
                //this.cyclothymiaSupervisorCommentTxtBox.Value = ds.Contacts[0].CyclothymiaSupervisorComment;
                //this.dipolicTxtBox.Value = ds.Contacts[0].Dipolic;
                //this.dipolicTherapistCommentTxtBox.Value = ds.Contacts[0].DipolicTherapistComment;
                //this.dipolicSupervisorCommentTxtBox.Value = ds.Contacts[0].DipolicSupervisorComment;
                //this.melancholicChkBox.Checked = ds.Contacts[0].Melancholic;
                //this.melancholicTherapistCommentTxtBox.Value = ds.Contacts[0].MelancholicTherapistComment;
                //this.melancholicSupervisorCommentTxtBox.Value = ds.Contacts[0].MelancholicSupervisorComment;
                //this.SetValueOnSelectControl(this.dysthymiaDDL, ds.Contacts[0].Dysthymia);
                //this.dysthymiaTherapistCommentTxtBox.Value = ds.Contacts[0].DysthymiaTherapistComment;
                //this.dysthymiaSupervisorCommentTxtBox.Value = ds.Contacts[0].DysthymiaSupervisorComment;
                //this.catatonicChkBox.Checked = ds.Contacts[0].Catatonic;
                //this.catatonicTherapistCommentTxtBox.Value = ds.Contacts[0].CatatonicTherapistComment;
                //this.catatonicSupervisorCommentTxtBox.Value = ds.Contacts[0].CatatonicSupervisorComment;
                //this.therapyResistantChkBox.Checked = ds.Contacts[0].TherapyResistant;
                //this.therapyResistantTherapistCommentTxtBox.Value = ds.Contacts[0].********************************;
                //this.therapyResistantSupervisorCommentTxtBox.Value = ds.Contacts[0].TherapyResistantSupervisorComment;
                //this.regressionChkBox.Checked = ds.Contacts[0].Regression;
                //this.regressionTherapistCommentTxtBox.Value = ds.Contacts[0].RegressionTherapistComment;
                //this.regressionSupervisorCommentTxtBox.Value = ds.Contacts[0].RegressionSupervisorComment;
                //this.chronicChkBox.Checked = ds.Contacts[0].Chronic;
                //this.chronicTherapistCommentTxtBox.Value = ds.Contacts[0].ChronicTherapistComment;
                //this.chronicSupervisorCommentTxtBox.Value = ds.Contacts[0].ChronicSupervisorComment;
                //this.seasonalChkBox.Checked = ds.Contacts[0].Seasonal;
                //this.seasonalTherapistCommentTxtBox.Value = ds.Contacts[0].SeasonalTherapistComment;
                //this.seasonalSupervisorCommentTxtBox.Value = ds.Contacts[0].SeasonalSupervisorComment;
                //this.epilochiaChkBox.Checked = ds.Contacts[0].Epilochia;
                //this.epilochiaTherapistCommentTxtBox.Value = ds.Contacts[0].EpilochiaTherapistComment;
                //this.epilochiaSupervisorCommentTxtBox.Value = ds.Contacts[0].EpilochiaSupervisorComment;
                //this.pregnancyChkBox.Checked = ds.Contacts[0].Pregnancy;
                //this.pregnancyTherapistCommentTxtBox.Value = ds.Contacts[0].PregnancyTherapistComment;
                //this.pregnancySupervisorCommentTxtBox.Value = ds.Contacts[0].PregnancySupervisorComment;
                //this.SetValueOnSelectControl(this.thirdAgeDDL, ds.Contacts[0].ThirdAge);
                //this.thirdAgeTherapistCommentTxtBox.Value = ds.Contacts[0].ThirdAgeTherapistComment;
                //this.thirdAgeSupervisorCommentTxtBox.Value = ds.Contacts[0].ThirdAgeSupervisorComment;
                //this.SetValueOnSelectControl(this.informalDDL, ds.Contacts[0].Informal);
                //this.informalTherapistCommentTxtBox.Value = ds.Contacts[0].InformalTherapistComment;
                //this.informalSupervisorCommentTxtBox.Value = ds.Contacts[0].InformalSupervisorComment;
                //this.SetValueOnSelectControl(this.psychoticDepressionDDL, ds.Contacts[0].PsychoticDepression);
                //this.psychoticDepressionTherapistCommentTxtBox.Value = ds.Contacts[0].PsychoticDepressionTherapistComment;
                //this.psychoticDepressionSupervisorCommentTxtBox.Value = ds.Contacts[0].PsychoticDepressionSupervisorComment;
                //this.elassonDepressiveDisorderChkBox.Checked = ds.Contacts[0].ElassonDepressiveDisorder;
                //this.elassonDepressiveDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].ElassonDepressiveDisorderTherapistComment;
                //this.elassonDepressiveDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].ElassonDepressiveDisorderSupervisorComment;
                //this.shortIntermmitentFormChkBox.Checked = ds.Contacts[0].ShortIntermmitentForm;
                //this.shortIntermmitentFormTherapistCommentTxtBox.Value = ds.Contacts[0].ShortIntermmitentFormTherapistComment;
                //this.shortIntermmitentFormSupervisorCommentTxtBox.Value = ds.Contacts[0].ShortIntermmitentFormSupervisorComment;
                //this.premenstrualDysphoricDisorderChkBox.Checked = ds.Contacts[0].PremenstrualDysphoricDisorder;
                //this.premenstrualDysphoricDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].PremenstrualDysphoricDisorderTherapistComment;
                //this.premenstrualDysphoricDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].PremenstrualDysphoricDisorderSupervisorComment;
                //this.depressivePseudonoiaChkBox.Checked = ds.Contacts[0].DepressivePseudonoia;
                //this.depressivePseudonoiaTherapistCommentTxtBox.Value = ds.Contacts[0].DepressivePseudonoiaTherapistComment;
                //this.depressivePseudonoiaSupervisorCommentTxtBox.Value = ds.Contacts[0].DepressivePseudonoiaSupervisorComment;
                this.otherMoodObservationsTxtBox.Value = ds.Contacts[0].OtherMoodObservations;
                this.otherMoodObservationsTherapistCommentTxtBox.Value = ds.Contacts[0].OtherMoodObservationsTherapistComment;
                this.otherMoodObservationsSupervisorCommentTxtBox.Value = ds.Contacts[0].OtherMoodObservationsSupervisorComment;

                this.SetValueOnSelectControl(this.anxietyDDL, ds.Contacts[0].Anxiety);
                this.anxietyTherapistCommentTxtBox.Value = ds.Contacts[0].AnxietyTherapistComment;
                this.anxietySupervisorCommentTxtBox.Value = ds.Contacts[0].AnxietySupervisorComment;

                //this.specificFearsTxtBox.Value = ds.Contacts[0].SpecificFears;
                //this.specificFearsTherapistCommentTxtBox.Value = ds.Contacts[0].SpecificFearsTherapistComment;
                //this.specificFearsSupervisorCommentTxtBox.Value = ds.Contacts[0].SpecificFearsSupervisorComment;
                //this.socialPhobiaTxtBox.Value = ds.Contacts[0].SocialPhobia;
                //this.socialPhobiaTherapistCommentTxtBox.Value = ds.Contacts[0].SocialPhobiaTherapistComment;
                //this.socialPhobiaSupervisorCommentTxtBox.Value = ds.Contacts[0].SocialPhobiaSupervisorComment;
                //this.panicDisorderTxtBox.Value = ds.Contacts[0].PanicDisorder;
                //this.panicDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].PanicDisorderTherapistComment;
                //this.panicDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].PanicDisorderSupervisorComment;
                //this.agoraphobiaTxtBox.Value = ds.Contacts[0].Agoraphobia;
                //this.agoraphobiaTherapistCommentTxtBox.Value = ds.Contacts[0].AgoraphobiaTherapistComment;
                //this.agoraphobiaSupervisorCommentTxtBox.Value = ds.Contacts[0].AgoraphobiaSupervisorComment;
                //this.anxietyPhysicalSymptomsDDL.Value = ds.Contacts[0].AnxietyPhysicalSymptoms;
                //this.SetValueOnSelectControl(this.anxietyPhysicalSymptomsDDL, ds.Contacts[0].AnxietyPhysicalSymptoms);
                //this.anxietyPhysicalSymptomsTherapistCommentTxtBox.Value = ds.Contacts[0].AnxietyPhysicalSymptomsTherapistComment;
                //this.anxietyPhysicalSymptomsSupervisorCommentTxtBox.Value = ds.Contacts[0].AnxietyPhysicalSymptomsSupervisorComment;
                //this.generalizedAxietyChkBox.Checked = ds.Contacts[0].GeneralizedAxiety;
                //this.generalizedAxietyTherapistCommentTxtBox.Value = ds.Contacts[0].GeneralizedAxietyTherapistComment;
                //this.generalizedAxietySupervisorCommentTxtBox.Value = ds.Contacts[0].GeneralizedAxietySupervisorComment;
                //this.SetValueOnSelectControl(this.obsessiveIdeasDDL, ds.Contacts[0].ObsessiveIdeas);
                //this.obsessiveIdeasTherapistCommentTxtBox.Value = ds.Contacts[0].ObsessiveIdeasTherapistComment;
                //this.obsessiveIdeasSupervisorCommentTxtBox.Value = ds.Contacts[0].ObsessiveIdeasSupervisorComment;
                //this.SetValueOnSelectControl(this.ideoPsychoComplusionsDDL, ds.Contacts[0].IdeoPsychoComplusions);
                //this.ideoPsychoComplusionsTherapistCommentTxtBox.Value = ds.Contacts[0].IdeoPsychoComplusionsTherapistComment;
                //this.ideoPsychoComplusionsSupervisorCommentTxtBox.Value = ds.Contacts[0].IdeoPsychoComplusionsSupervisorComment;
                //this.tikChkBox.Checked = ds.Contacts[0].Tik;
                //this.tikTherapistCommentTxtBox.Value = ds.Contacts[0].TikTherapistComment;
                //this.tikSupervisorCommentTxtBox.Value = ds.Contacts[0].TikSupervisorComment;
                //this.SetValueOnSelectControl(this.dysmorphobiaDDL, ds.Contacts[0].Dysmorphobia);
                //this.dysmorphobiaTherapistCommentTxtBox.Value = ds.Contacts[0].DysmorphobiaTherapistComment;
                //this.dysmorphobiaSupervisorCommentTxtBox.Value = ds.Contacts[0].DysmorphobiaSupervisorComment;
                //this.paracumulationDisorderChkBox.Checked = ds.Contacts[0].ParacumulationDisorder;
                //this.paracumulationDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].ParacumulationDisorderTherapistComment;
                //this.paracumulationDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].ParacumulationDisorderSupervisorComment;
                //this.trichotillomaniaChkBox.Checked = ds.Contacts[0].Trichotillomania;
                //this.trichotillomaniaTherapistCommentTxtBox.Value = ds.Contacts[0].********************************;
                //this.trichotillomaniaSupervisorCommentTxtBox.Value = ds.Contacts[0].TrichotillomaniaSupervisorComment;
                //this.postTraumaticStressTxtBox.Value = ds.Contacts[0].PostTraumaticStress;
                //this.postTraumaticStressTherapistCommentTxtBox.Value = ds.Contacts[0].PostTraumaticStressTherapistComment;
                //this.postTraumaticStressSupervisorCommentTxtBox.Value = ds.Contacts[0].PostTraumaticStressSupervisorComment;
                //this.otherStressObservationsTxtBox.Value = ds.Contacts[0].OtherStressObservations;
                //this.otherStressObservationsTherapistCommentTxtBox.Value = ds.Contacts[0].OtherStressObservationsTherapistComment;
                //this.otherStressObservationsSupervisorCommentTxtBox.Value = ds.Contacts[0].OtherStressObservationsSupervisorComment;

                this.SetValueOnSelectControl(this.sleepDDL, ds.Contacts[0].Sleep);
                this.sleepTherapistCommentTxtBox.Value = ds.Contacts[0].SleepTherapistComment;
                this.sleepSupervisorCommentTxtBox.Value = ds.Contacts[0].SleepSupervisorComment;
                //this.SetValueOnSelectControl(this.insomniaDDL, ds.Contacts[0].Insomnia);
                //this.insomniaTherapistCommentTxtBox.Value = ds.Contacts[0].InsomniaTherapistComment;
                //this.insomniaSupervisorCommentTxtBox.Value = ds.Contacts[0].InsomniaSupervisorComment;
                //this.SetValueOnSelectControl(this.sleepDisorderDDL, ds.Contacts[0].SleepDisorder);
                //this.sleepDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].SleepDisorderTherapistComment;
                //this.sleepDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].SleepDisorderSupervisorComment;
                //this.otherSleepObservationsTxtBox.Value = ds.Contacts[0].OtherSleepObservations;
                //this.otherSleepObservationsTherapistCommentTxtBox.Value = ds.Contacts[0].OtherSleepObservationsTherapistComment;
                //this.otherSleepObservationsSupervisorCommentTxtBox.Value = ds.Contacts[0].OtherSleepObservationsSupervisorComment;

                this.SetValueOnSelectControl(this.psychoticDDL, ds.Contacts[0].Psychotic);
                this.psychoticTherapistCommentTxtBox.Value = ds.Contacts[0].PsychoticTherapistComment;
                this.psychoticSupervisorCommentTxtBox.Value = ds.Contacts[0].PsychoticSupervisorComment;
                this.SetValueOnSelectControl(this.defenseMechanismsDDL, ds.Contacts[0].DefenseMechanisms);
                this.defenseMechanismsTherapistCommentTxtBox.Value = ds.Contacts[0].DefenseMechanismsTherapistComment;
                this.defenseMechanismsSupervisorCommentTxtBox.Value = ds.Contacts[0].DefenseMechanismsSupervisorComment;
                this.SetValueOnSelectControl(this.shapesDDL, ds.Contacts[0].Shapes);
                this.shapesTherapistCommentTxtBox.Value = ds.Contacts[0].ShapesTherapistComment;
                this.shapesSupervisorCommentTxtBox.Value = ds.Contacts[0].ShapesSupervisorComment;
                //this.phychoticSymptomsTxtBox.Value = ds.Contacts[0].PhychoticSymptoms;
                //this.phychoticSymptomsTherapistCommentTxtBox.Value = ds.Contacts[0].PhychoticSymptomsTherapistComment;
                //this.phychoticSymptomsSupervisorCommentTxtBox.Value = ds.Contacts[0].PhychoticSymptomsSupervisorComment;
                //this.schizophreniaTxtBox.Value = ds.Contacts[0].Schizophrenia;
                //this.schizophreniaTherapistCommentTxtBox.Value = ds.Contacts[0].SchizophreniaTherapistComment;
                //this.schizophreniaSupervisorCommentTxtBox.Value = ds.Contacts[0].SchizophreniaSupervisorComment;
                //this.schizoemotionalTxtBox.Value = ds.Contacts[0].Schizoemotional;
                //this.schizoemotionalTherapistCommentTxtBox.Value = ds.Contacts[0].SchizoemotionalTherapistComment;
                //this.schizoemotionalSupervisorCommentTxtBox.Value = ds.Contacts[0].SchizoemotionalSupervisorComment;
                //this.delirusiveTxtBox.Value = ds.Contacts[0].Delirusive;
                //this.delirusiveTherapistCommentTxtBox.Value = ds.Contacts[0].DelirusiveTherapistComment;
                //this.delirusiveSupervisorCommentTxtBox.Value = ds.Contacts[0].DelirusiveSupervisorComment;
                //this.sortPshychoticTxtBox.Value = ds.Contacts[0].SortPshychotic;
                //this.sortPshychoticTherapistCommentTxtBox.Value = ds.Contacts[0].SortPshychoticTherapistComment;
                //this.sortPshychoticSupervisorCommentTxtBox.Value = ds.Contacts[0].SortPshychoticSupervisorComment;
                //this.organicPhsychosyndromeTxtBox.Value = ds.Contacts[0].OrganicPhsychosyndrome;
                //this.organicPhsychosyndromeTherapistCommentTxtBox.Value = ds.Contacts[0].OrganicPhsychosyndromeTherapistComment;
                //this.organicPhsychosyndromeSupervisorCommentTxtBox.Value = ds.Contacts[0].OrganicPhsychosyndromeSupervisorComment;
                //this.schizophrenicoformTxtBox.Value = ds.Contacts[0].Schizophrenicoform;
                //this.schizophrenicoformTherapistCommentTxtBox.Value = ds.Contacts[0].SchizophrenicoformTherapistComment;
                //this.schizophrenicoformSupervisorCommentTxtBox.Value = ds.Contacts[0].SchizophrenicoformSupervisorComment;
                //this.persistentDisorderTxtBox.Value = ds.Contacts[0].PersistentDisorder;
                //this.persistentDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].PersistentDisorderTherapistComment;
                //this.persistentDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].PersistentDisorderSupervisorComment;
                //this.physicalConditionDisorderTxtBox.Value = ds.Contacts[0].PhysicalConditionDisorder;
                //this.physicalConditionDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].PhysicalConditionDisorderTherapistComment;
                //this.physicalConditionDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].PhysicalConditionDisorderSupervisorComment;
                //this.nonSpecifiedDisorderTxtBox.Value = ds.Contacts[0].NonSpecifiedDisorder;
                //this.nonSpecifiedDisorderTherapistCommentTxtBox.Value = ds.Contacts[0].NonSpecifiedDisorderTherapistComment;
                //this.nonSpecifiedDisorderSupervisorCommentTxtBox.Value = ds.Contacts[0].NonSpecifiedDisorderSupervisorComment;

                this.SetValueOnSelectControl(this.paranoidDDL, ds.Contacts[0].Paranoid);
                this.paranoidTherapistCommentTxtBox.Value = ds.Contacts[0].ParanoidTherapistComment;
                this.paranoidSupervisorCommentTxtBox.Value = ds.Contacts[0].ParanoidSupervisorComment;
                //this.schizoidDDL.Value = ds.Contacts[0].Schizoid;
                this.SetValueOnSelectControl(this.schizoidDDL, ds.Contacts[0].Schizoid);
                this.schizoidTherapistCommentTxtBox.Value = ds.Contacts[0].SchizoidTherapistComment;
                this.schizoidSupervisorCommentTxtBox.Value = ds.Contacts[0].SchizoidSupervisorComment;
                //this.schizotypeDDL.Value = ds.Contacts[0].Schizotype;
                this.SetValueOnSelectControl(this.schizotypeDDL, ds.Contacts[0].Schizotype);
                this.schizotypeTherapistCommentTxtBox.Value = ds.Contacts[0].SchizotypeTherapistComment;
                this.schizotypeSupervisorCommentTxtBox.Value = ds.Contacts[0].SchizotypeSupervisorComment;
                //this.onLimitDDL.Value = ds.Contacts[0].OnLimit;
                this.SetValueOnSelectControl(this.onLimitDDL, ds.Contacts[0].OnLimit);
                this.onLimitTherapistCommentTxtBox.Value = ds.Contacts[0].OnLimitTherapistComment;
                this.onLimitSupervisorCommentTxtBox.Value = ds.Contacts[0].OnLimitSupervisorComment;
                //this.antisocialDDL.Value = ds.Contacts[0].Antisocial;
                this.SetValueOnSelectControl(this.antisocialDDL, ds.Contacts[0].Antisocial);
                this.antisocialTherapistCommentTxtBox.Value = ds.Contacts[0].AntisocialTherapistComment;
                this.antisocialSupervisorCommentTxtBox.Value = ds.Contacts[0].AntisocialSupervisorComment;
                //this.histronicDDL.Value = ds.Contacts[0].Histronic;
                this.SetValueOnSelectControl(this.histronicDDL, ds.Contacts[0].Histronic);
                this.histronicTherapistCommentTxtBox.Value = ds.Contacts[0].HistronicTherapistComment;
                this.histronicSupervisorCommentTxtBox.Value = ds.Contacts[0].HistronicSupervisorComment;
                //this.narcissisticDDL.Value = ds.Contacts[0].Narcissistic;
                this.SetValueOnSelectControl(this.narcissisticDDL, ds.Contacts[0].Narcissistic);
                this.narcissisticTherapistCommentTxtBox.Value = ds.Contacts[0].NarcissisticTherapistComment;
                this.narcissisticSupervisorCommentTxtBox.Value = ds.Contacts[0].NarcissisticSupervisorComment;
                //this.ideopsychocompressionDDL.Value = ds.Contacts[0].Ideopsychocompression;
                this.SetValueOnSelectControl(this.ideopsychocompressionDDL, ds.Contacts[0].Ideopsychocompression);
                this.ideopsychocompressionTherapistCommentTxtBox.Value = ds.Contacts[0].IdeopsychocompressionTherapistComment;
                this.ideopsychocompressionSupervisorCommentTxtBox.Value = ds.Contacts[0].IdeopsychocompressionSupervisorComment;
                //this.avoidableDDL.Value = ds.Contacts[0].Avoidable;
                this.SetValueOnSelectControl(this.avoidableDDL, ds.Contacts[0].Avoidable);
                this.avoidableTherapistCommentTxtBox.Value = ds.Contacts[0].AvoidableTherapistComment;
                this.avoidableSupervisorCommentTxtBox.Value = ds.Contacts[0].AvoidableSupervisorComment;
                //this.addictiveDDL.Value = ds.Contacts[0].Addictive;
                this.SetValueOnSelectControl(this.addictiveDDL, ds.Contacts[0].Addictive);
                this.addictiveTherapistCommentTxtBox.Value = ds.Contacts[0].AddictiveTherapistComment;
                this.addictiveSupervisorCommentTxtBox.Value = ds.Contacts[0].AddictiveSupervisorComment;
                //this.passiveAggressiveDDL.Value = ds.Contacts[0].PassiveAggressive;
                this.SetValueOnSelectControl(this.passiveAggressiveDDL, ds.Contacts[0].PassiveAggressive);
                this.passiveAggressiveTherapistCommentTxtBox.Value = ds.Contacts[0].PassiveAggressiveTherapistComment;
                this.passiveAggressiveSupervisorCommentTxtBox.Value = ds.Contacts[0].PassiveAggressiveSupervisorComment;
                this.otherDisorderInfoTxtBox.Value = ds.Contacts[0].OtherDisorderInfo;
                this.otherDisorderInfoTherapistCommentTxtBox.Value = ds.Contacts[0].OtherDisorderInfoTherapistComment;
                this.otherDisorderInfoSupervisorCommentTxtBox.Value = ds.Contacts[0].OtherDisorderInfoSupervisorComment;

                //Tab Questionnairies
                this.questionnairiesGrid.DataSource = ds.Questionnaires;
                this.questionnairiesGrid.DataBind();

                //Tab Appointments
                //Αν δεν έχει ήδη προστεθεί η στήλη BackgroundColor
                if (ds.Appointments.Columns.IndexOf("BackgroundColor") == -1)
                {
                    ds.Appointments.Columns.Add("BackgroundColor", typeof(string), "");

                    DataTable roomsDT = Data.FieldValuesMappings.DataSet.Tables["Appointments-AppointmentRooms"].Copy();
                    foreach (MentalViewDataSet.AppointmentsRow appointmentsRow in ds.Appointments.Rows)
                    {
                        if (appointmentsRow.Room != "")
                        {
                            if (roomsDT.Select("RoomId='" + appointmentsRow.Room + "'").Count() > 0)
                            {
                                appointmentsRow["BackgroundColor"] = roomsDT.Select("RoomId='" + appointmentsRow.Room + "'").First()["BackColor"];
                            }
                        }
                    }
                }
                foreach (MentalViewDataSet.AppointmentsRow appointmentsRow in ds.Appointments)
                {
                    appointmentsRow.TaskSupervision = false;
                    appointmentsRow.TaskSupervisionCustomers = "";
                    appointmentsRow.TaskSupervisionReplies = "";
                    appointmentsRow.TaskSupervisionSubject = "";
                    appointmentsRow.TaskSupervisionTherapistIDs = "";
                }
                #region  Αφαιρούμε κάποιες στήλες που δεν χρειάζονται γιατί δημιουργουν πρόβλημα στο appoitmentsGrid.
                //if (ds.Appointments.Columns.Contains("TaskSupervision"))
                //{
                //    ds.Appointments.Columns.Remove("TaskSupervision");
                //}
                //if (ds.Appointments.Columns.Contains("TaskSupervisionTherapistIDs"))
                //{
                //    ds.Appointments.Columns.Remove("TaskSupervisionTherapistIDs");
                //}
                //if (ds.Appointments.Columns.Contains("TaskSupervisionCustomers"))
                //{
                //    ds.Appointments.Columns.Remove("TaskSupervisionCustomers");
                //}
                //if (ds.Appointments.Columns.Contains("TaskSupervisionCustomers"))
                //{
                //    ds.Appointments.Columns.Remove("TaskSupervisionCustomers");
                //}
                //if (ds.Appointments.Columns.Contains("TaskSupervisionSubject"))
                //{
                //    ds.Appointments.Columns.Remove("TaskSupervisionSubject");
                //}
                #endregion

                this.appointmentsGrid.DataSource = ds.Appointments; //ds.Appointments.Select( x => new { AppointmentId= x.AppointmentId, AllDay= x.AllDay });
                this.appointmentsGrid.DataBind();

                //if (Session["SelectedQuestionnaireId"] != null)
                //{
                //    Int64 questionnaireId = Convert.ToInt64(Session["SelectedQuestionnaireId"]);
                //    this.questionnaireQuestionsGrid.DataSource = new DataView(ds.QuestionnaireQuestions, "QuestionnaireId=" + questionnaireId.ToString(), "", DataViewRowState.CurrentRows).ToTable();
                //}

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void GetDataFromUIControls(ref MentalViewDataSet ds)
        {
            try
            {
                //this.RetrieveValueFromPageForm(this.newQuestionnaire33DDL.ID);
                //string text = Request.Form["ctl00$ctl00$includeFilesBody$mainBody$contactTab$newQuestionnaire33DDL"];

                ds.Contacts[0].FirstName = this.firstNameTxtBox.Value;
                ds.Contacts[0].LastName = this.lastNameTxtBox.Value;
                ds.Contacts[0].Active = this.activeChkBox.Checked;
                ds.Contacts[0].InactiveReason = this.RetrieveSelect2ValueFromPageForm(this.inactiveReasonDDL.ID);
                ds.Contacts[0].Waiting = this.waitingChkBox.Checked;

                //if (string.IsNullOrEmpty(this.stateDDL.Value) == false)
                //{
                //    if (Convert.ToInt64(this.stateDDL.Value) > 0)
                //    {
                ds.Contacts[0].AppointmentsState = this.stateDDL.Value == null ? "" : this.stateDDL.Value;
                //}
                //else
                //{
                //    ds.Contacts[0].AppointmentsState ="";
                //}
                //}

                if (string.IsNullOrEmpty(this.appointmentCategoryIdDDL.Value) == false)
                {
                    if (Convert.ToInt64(this.appointmentCategoryIdDDL.Value) > 0)
                    {
                        ds.Contacts[0].AppointmentCategoryId = Convert.ToInt64(this.appointmentCategoryIdDDL.Value);
                    }
                    else
                    {
                        ds.Contacts[0].SetAppointmentCategoryIdNull();
                    }
                }

                string therapistId = this.RetrieveSelect2ValueFromPageForm(this.therapistIdDDL.ID);
                if (string.IsNullOrEmpty(therapistId) == false && therapistId != "-1")
                {
                    ds.Contacts[0].TherapistId = Convert.ToInt64(therapistId);
                }
                else
                {
                    ds.Contacts[0].SetTherapistIdNull();
                }

                string coTherapistId = this.RetrieveSelect2ValueFromPageForm(this.coTherapistIdDDL.ID);
                if (string.IsNullOrEmpty(coTherapistId) == false && coTherapistId != "-1")
                {
                    ds.Contacts[0].CoTherapistId = Convert.ToInt64(coTherapistId);
                }
                else
                {
                    ds.Contacts[0].SetCoTherapistIdNull();
                }

                string guestId = this.RetrieveSelect2ValueFromPageForm(this.guestIdDDL.ID);
                if (string.IsNullOrEmpty(guestId) == false && guestId != "-1")
                {
                    ds.Contacts[0].GuestId = Convert.ToInt64(guestId);
                }
                else
                {
                    ds.Contacts[0].SetGuestIdNull();
                }

                string clinicSupervisorId = this.RetrieveSelect2ValueFromPageForm(this.clinicSupervisorIdDDL.ID);
                if (string.IsNullOrEmpty(clinicSupervisorId) == false && clinicSupervisorId != "-1")
                {
                    ds.Contacts[0].ClinicSupervisorId = Convert.ToInt64(clinicSupervisorId);
                }
                else
                {
                    ds.Contacts[0].SetClinicSupervisorIdNull();
                }

                string diagnosticianId = this.RetrieveSelect2ValueFromPageForm(this.diagnosticianIdDDL.ID);
                if (string.IsNullOrEmpty(diagnosticianId) == false && diagnosticianId != "-1")
                {
                    ds.Contacts[0].DiagnosticianId = Convert.ToInt64(diagnosticianId);
                }
                else
                {
                    ds.Contacts[0].SetDiagnosticianIdNull();
                }

                if (this.psychotherapyStartDateTextBox.Text != "")
                {
                    ds.Contacts[0].PsychotherapyStartDate = DateTime.Parse(this.psychotherapyStartDateTextBox.Text, new CultureInfo("el-GR"));
                }
                else
                {
                    ds.Contacts[0].SetPsychotherapyStartDateNull();
                }
                if (this.birthDateTxtBox.Text != "")
                {
                    ds.Contacts[0].BirthDate = DateTime.Parse(this.birthDateTxtBox.Text, new CultureInfo("el-GR"));
                }
                else
                {
                    ds.Contacts[0].SetBirthDateNull();
                }

                ds.Contacts[0].SessionOrigin = this.RetrieveSelect2ValueFromPageForm(this.sessionOriginDDL.ID);
                ds.Contacts[0].Sex = this.RetrieveSelect2ValueFromPageForm(this.sexDDL.ID);
                ds.Contacts[0].Phone1 = this.phone1TxtBox.Value;
                ds.Contacts[0].Mobile1 = this.mobile1TxtBox.Value;
                ds.Contacts[0].EmergencyPhone = this.emergencyPhoneTxtBox.Value;
                ds.Contacts[0].EmergencyContactName = this.emergencyContactNameTxtBox.Value;
                ds.Contacts[0].Email = this.email1TxtBox.Value;
                ds.Contacts[0].Email2 = this.email2TxtBox.Value;
                ds.Contacts[0].BirthPlace = this.birthPlaceTxtBox.Value;
                ds.Contacts[0].Residence = this.residenceTxtBox.Value;
                ds.Contacts[0].EducationLevel = this.RetrieveSelect2ValueFromPageForm(this.educationLevelDDL.ID);
                ds.Contacts[0].MaritalStatus = this.RetrieveSelect2ValueFromPageForm(this.maritalStatusDDL.ID);
                ds.Contacts[0].Children = this.childrenTxtBox.Value;
                ds.Contacts[0].ChildrenAge = this.childrenAgeTxtBox.Value;
                ds.Contacts[0].LivingStatus = this.RetrieveSelect2ValueFromPageForm(this.livingStatusDDL.ID);
                ds.Contacts[0].Occupation = this.RetrieveSelect2ValueFromPageForm(this.occupationDDL.ID);
                ds.Contacts[0].EconomicStatus = this.RetrieveSelect2ValueFromPageForm(this.economicStatusDDL.ID);
                ds.Contacts[0].CommunicationMethod = this.RetrieveSelect2ValueFromPageForm(this.communicationMethodDDL.ID);
                ds.Contacts[0].SessionFrequency = this.RetrieveSelect2ValueFromPageForm(this.sessionFrequencyDDL.ID);
                ds.Contacts[0].ReferralToAnotherSpecialist = this.RetrieveSelect2ValueFromPageForm(this.referralToAnotherSpecialistDDL.ID);
                ds.Contacts[0].Notes = this.notesTxtBox.Text;
                ds.Contacts[0].CaseFormulation = this.caseFormulationTxtBox.Text;
                ds.Contacts[0].SpecialistObservations = this.specilistObservationsTxtBox.Text;
                ds.Contacts[0].AccessLevel = this.RetrieveSelect2ValueFromPageForm(this.accessLevelDDL.ID);
                ds.Contacts[0].Skills = this.skillsTxtBox.Value;
                ds.Contacts[0].TraumaticHistory = this.traumaticHistoryTxtBox.Value;
                ds.Contacts[0].NegativeBeliefs = this.negativeBeliefsTxtBox.Value;
                ds.Contacts[0].NegativeEmotions = this.negativeEmotionsTxtBox.Value;
                ds.Contacts[0].TriggeringEvents = this.triggeringEventsTxtBox.Value;
                ds.Contacts[0].DysfunctionalBehaviors = this.dysfunctionalBehaviorsTxtBox.Value;
                ds.Contacts[0].SecondaryBenefit = this.secondaryBenefitTxtBox.Value;
                ds.Contacts[0].EmotionalProfile = this.RetrieveSelect2ValueFromPageForm(this.emotionalProfileDDL.ID);
                ds.Contacts[0].QuestionnairiesLink = this.questionnairiesLinkTxtBox.Value;

                ////OnlineIntervations listview
                //string onlineIntervations = "";
                ////foreach (int nodeIndex in this.onlineIntervationsTreeView.CheckedNodes)
                ////{
                ////    onlineIntervations += this.onlineIntervationsTreeView.Nodes[nodeIndex].Id + "|";
                ////}
                //if (onlineIntervations.Length > 0)
                //{
                //    onlineIntervations = onlineIntervations.Substring(0, onlineIntervations.Length - 1);
                //}
                //ds.Contacts[0].OnlineIntervations = onlineIntervations;

                //2o tab - Personal & Medical Info
                ds.Contacts[0].EyeContact = this.RetrieveSelect2ValueFromPageForm(this.eyeContactDDL.ID);
                ds.Contacts[0].BodyLanguage = this.RetrieveSelect2ValueFromPageForm(this.bodyLanguageDDL.ID);
                ds.Contacts[0].VoiceTone = this.RetrieveSelect2ValueFromPageForm(this.voiceToneDDL.ID);
                ds.Contacts[0].Narration = this.RetrieveSelect2ValueFromPageForm(this.narrationDDL.ID);
                ds.Contacts[0].SexualOrientation = this.RetrieveSelect2ValueFromPageForm(this.sexualOrientationDDL.ID);
                ds.Contacts[0].GeneralRequest = this.RetrieveSelect2ValueFromPageForm(this.generalRequestDDL.ID);
                ds.Contacts[0].SpecialRequest = this.specialRequestTxtBox.Value;
                ds.Contacts[0].HealthHistory = this.healthHistoryTxtBox.Value;
                ds.Contacts[0].Medication = this.medicationTxtBox.Value;
                ds.Contacts[0].HealingExperience = this.RetrieveSelect2ValueFromPageForm(this.healingExperienceDDL.ID);
                ds.Contacts[0].OtherActivities = this.otherActivitiesTxtBox.Value;
                ds.Contacts[0].TherapistFirstView = this.therapistFirstViewTxtBox.Value;
                ds.Contacts[0].AppointmentsStart = this.RetrieveSelect2ValueFromPageForm(this.appointmentsStartDDL.ID);
                ds.Contacts[0].AppointmentsFrequency = this.RetrieveSelect2ValueFromPageForm(this.appointmentsFrequencyDDL.ID);
                ds.Contacts[0].InterventionModel = this.RetrieveSelect2ValueFromPageForm(this.interventionModelDDL.ID);
                if (this.lastMedicalCheckupDateTxtBox.Text != "")
                {
                    ds.Contacts[0].LastMedicalCheckupDate = DateTime.Parse(this.lastMedicalCheckupDateTxtBox.Text, new CultureInfo("el-GR"));
                }
                else
                {
                    ds.Contacts[0].SetLastMedicalCheckupDateNull();
                }
                ds.Contacts[0].LastMedicalCheckup = this.lastMedicalCheckupTxtBox.Value;

                //3o tab - Biographic Information
                ds.Contacts[0].MotherCharacteristics = this.RetrieveSelect2ValueFromPageForm(this.motherCharacteristicsDDL.ID);
                ds.Contacts[0].MotherCharacteristicsTherapistComment = this.motherCharacteristicsTherapistCommentTxtBox.Value;
                ds.Contacts[0].MotherCharacteristicsSupervisorComment = this.motherCharacteristicsSupervisorCommentTxtBox.Value;
                ds.Contacts[0].MotherInfo = this.motherInfoTxtBox.Value;
                ds.Contacts[0].MotherInfoTherapistComment = this.motherInfoTherapistCommentTxtBox.Value;
                ds.Contacts[0].MotherInfoSupervisorComment = this.motherInfoSupervisorCommentTxtBox.Value;
                ds.Contacts[0].OtherImportantFromMotherFamily = this.RetrieveSelect2ValueFromPageForm(this.otherImportantFromMotherFamilyDDL.ID);
                ds.Contacts[0].OtherImportantFromMotherFamilyTherapistComment = this.otherImportantFromMotherFamilyTherapistCommentTxtBox.Value;
                ds.Contacts[0].OtherImportantFromMotherFamilySupervisorComment = this.otherImportantFromMotherFamilySupervisorCommentTxtBox.Value;
                ds.Contacts[0].FatherCharacteristics = this.RetrieveSelect2ValueFromPageForm(this.fatherCharacteristicsDDL.ID);
                ds.Contacts[0].FatherCharacteristicsTherapistComment = this.fatherCharacteristicsTherapistCommentTxtBox.Value;
                ds.Contacts[0].FatherCharacteristicsSupervisorComment = this.fatherCharacteristicsSupervisorCommentTxtBox.Value;
                ds.Contacts[0].FatherInfo = this.fatherInfoTxtBox.Value;
                ds.Contacts[0].FatherInfoTherapistComment = this.fatherInfoTherapistCommentTxtBox.Value;
                ds.Contacts[0].FatherInfoSupervisorComment = this.fatherInfoSupervisorCommentTxtBox.Value;
                ds.Contacts[0].OtherImportantFromFatherFamily = this.RetrieveSelect2ValueFromPageForm(this.otherImportantFromFatherFamilyDDL.ID);
                ds.Contacts[0].OtherImportantFromFatherFamilyTherapistComment = this.otherImportantFromFatherFamilyTherapistCommentTxtBox.Value;
                ds.Contacts[0].OtherImportantFromFatherFamilySupervisorComment = this.otherImportantFromFatherFamilySupervisorCommentTxtBox.Value;
                ds.Contacts[0].MotherFamilyHistory = this.RetrieveSelect2ValueFromPageForm(this.motherFamilyHistoryDDL.ID);
                ds.Contacts[0].MotherFamilyHistoryTherapistComment = this.motherFamilyHistoryTherapistCommentTxtBox.Value;
                ds.Contacts[0].MotherFamilyHistorySupervisorComment = this.motherFamilyHistorySupervisorCommentTxtBox.Value;
                ds.Contacts[0].FatherFamilyHistory = this.RetrieveSelect2ValueFromPageForm(this.fatherFamilyHistoryDDL.ID);
                ds.Contacts[0].FatherFamilyHistoryTherapistComment = this.fatherFamilyHistoryTherapistCommentTxtBox.Value;
                ds.Contacts[0].FatherFamilyHistorySupervisorComment = this.fatherFamilyHistorySupervisorCommentTxtBox.Value;
                ds.Contacts[0].FamilyMedicalHistory = this.familyMedicalHistoryTxtBox.Value;
                ds.Contacts[0].FamilyMedicalHistoryTherapistComment = this.familyMedicalHistoryTherapistCommentTxtBox.Value;
                ds.Contacts[0].FamilyMedicalHistorySupervisorComment = this.familyMedicalHistorySupervisorCommentTxtBox.Value;
                ds.Contacts[0].OtherImportant = this.RetrieveSelect2ValueFromPageForm(this.otherImportantDDL.ID);
                ds.Contacts[0].OtherImportantTherapistComment = this.otherImportantTherapistCommentTxtBox.Value;
                ds.Contacts[0].OtherImportantSupervisorComment = this.otherImportantSupervisorCommentTxtBox.Value;
                ds.Contacts[0].MotherFeedbackInMySuccess = this.RetrieveSelect2ValueFromPageForm(this.motherFeedbackInMySuccessDDL.ID);   //this.motherFeedbackInMySuccessDDL.Value == null ? "" : this.motherFeedbackInMySuccessDDL.Value;
                ds.Contacts[0].MotherFeedbackInMySuccessTherapistComment = this.motherFeedbackInMySuccessTherapistCommentTxtBox.Value;
                ds.Contacts[0].MotherFeedbackInMySuccessSupervisorComment = this.motherFeedbackInMySuccessSupervisorCommentTxtBox.Value;
                ds.Contacts[0].MotherFeedbackInMyFailure = this.RetrieveSelect2ValueFromPageForm(this.motherFeedbackInMyFailureDDL.ID);   //this.motherFeedbackInMyFailureDDL.Value == null ? "" : this.motherFeedbackInMyFailureDDL.Value;
                ds.Contacts[0].MotherFeedbackInMyFailureTherapistComment = this.motherFeedbackInMyFailureTherapistCommentTxtBox.Value;
                ds.Contacts[0].MotherFeedbackInMyFailureSupervisorComment = this.motherFeedbackInMyFailureSupervisorCommentTxtBox.Value;
                ds.Contacts[0].FatherFeedbackInMySuccess = this.RetrieveSelect2ValueFromPageForm(this.fatherFeedbackInMySuccessDDL.ID);   //this.fatherFeedbackInMySuccessDDL.Value == null ? "" : this.fatherFeedbackInMySuccessDDL.Value;
                ds.Contacts[0].FatherFeedbackInMySuccessTherapistComment = this.fatherFeedbackInMySuccessTherapistCommentTxtBox.Value;
                ds.Contacts[0].FatherFeedbackInMySuccessSupervisorComment = this.fatherFeedbackInMySuccessSupervisorCommentTxtBox.Value;
                ds.Contacts[0].FatherFeedbackInMyFailure = this.RetrieveSelect2ValueFromPageForm(this.fatherFeedbackInMyFailureDDL.ID);  // this.fatherFeedbackInMyFailureDDL.Value == null ? "" : this.fatherFeedbackInMyFailureDDL.Value;
                ds.Contacts[0].FatherFeedbackInMyFailureTherapistComment = this.fatherFeedbackInMyFailureTherapistCommentTxtBox.Value;
                ds.Contacts[0].FatherFeedbackInMyFailureSupervisorComment = this.fatherFeedbackInMyFailureSupervisorCommentTxtBox.Value;
                ds.Contacts[0].ImportantFeedbackInMySuccess = this.RetrieveSelect2ValueFromPageForm(this.importantFeedbackInMySuccessDDL.ID);   //this.importantFeedbackInMySuccessDDL.Value == null ? "" : this.importantFeedbackInMySuccessDDL.Value;
                ds.Contacts[0].ImportantFeedbackInMySuccessTherapistComment = this.importantFeedbackInMySuccessTherapistCommentTxtBox.Value;
                ds.Contacts[0].ImportantFeedbackInMySuccessSupervisorComment = this.importantFeedbackInMySuccessSupervisorCommentTxtBox.Value;
                ds.Contacts[0].ImportantFeedbackInMyFailure = this.RetrieveSelect2ValueFromPageForm(this.importantFeedbackInMyFailureDDL.ID);   //this.importantFeedbackInMyFailureDDL.Value == null ? "" : this.importantFeedbackInMyFailureDDL.Value;
                ds.Contacts[0].ImportantFeedbackInMyFailureTherapistComment = this.importantFeedbackInMyFailureTherapistCommentTxtBox.Value;
                ds.Contacts[0].ImportantFeedbackInMyFailureSupervisorComment = this.importantFeedbackInMyFailureSupervisorCommentTxtBox.Value;
                ds.Contacts[0].AdhesionType = this.RetrieveSelect2ValueFromPageForm(this.adhesionTypeDDL.ID);  //this.adhesionTypeDDL.Value == null ? "" : this.adhesionTypeDDL.Value;
                ds.Contacts[0].AdhesionTypeTherapistComment = this.adhesionTypeTherapistCommentTxtBox.Value;
                ds.Contacts[0].AdhesionTypeSupervisorComment = this.adhesionTypeSupervisorCommentTxtBox.Value;
                ds.Contacts[0].PreschoolExperiences = this.RetrieveSelect2ValueFromPageForm(this.preschoolExperiencesDDL.ID);
                ds.Contacts[0].PreschoolExperiencesTherapistComment = this.preschoolExperiencesTherapistCommentTxtBox.Value;
                ds.Contacts[0].PreschoolExperiencesSupervisorComment = this.preschoolExperiencesSupervisorCommentTxtBox.Value;
                ds.Contacts[0].SchoolExperiences = this.RetrieveSelect2ValueFromPageForm(this.schoolExperiencesDDL.ID);   //this.schoolExperiencesDDL.Value == null ? "" : this.schoolExperiencesDDL.Value;
                ds.Contacts[0].SchoolExperiencesTherapistComment = this.schoolExperiencesTherapistCommentTxtBox.Value;
                ds.Contacts[0].SchoolExperiencesSupervisorComment = this.schoolExperiencesSupervisorCommentTxtBox.Value;
                ds.Contacts[0].TeenageExperiences = this.RetrieveSelect2ValueFromPageForm(this.teenageExperiencesDDL.ID);   //this.teenageExperiencesDDL.Value == null ? "" : this.teenageExperiencesDDL.Value;
                ds.Contacts[0].TeenageExperiencesTherapistComment = this.teenageExperiencesTherapistCommentTxtBox.Value;
                ds.Contacts[0].TeenageExperiencesSupervisorComment = this.teenageExperiencesSupervisorCommentTxtBox.Value;
                ds.Contacts[0].AdultExperiences = this.RetrieveSelect2ValueFromPageForm(this.adultExperiencesDDL.ID);   //this.adultExperiencesDDL.Value == null ? "" : this.adultExperiencesDDL.Value;  //
                ds.Contacts[0].AdultExperiencesTherapistComment = this.adultExperiencesTherapistCommentTxtBox.Value;
                ds.Contacts[0].AdultExperiencesSupervisorComment = this.adultExperiencesSupervisorCommentTxtBox.Value;
                ds.Contacts[0].WorkExperiences = this.RetrieveSelect2ValueFromPageForm(this.workExperiencesDDL.ID);   //this.workExperiencesDDL.Value == null ? "" : this.workExperiencesDDL.Value;  
                ds.Contacts[0].WorkExperiencesTherapistComment = this.workExperiencesTherapistCommentTxtBox.Value;
                ds.Contacts[0].WorkExperiencesSupervisorComment = this.workExperiencesSupervisorCommentTxtBox.Value;
                ds.Contacts[0].GeneralTraumaHistory = this.generalTraumaHistoryTxtBox.Value;
                ds.Contacts[0].GeneralTraumaHistoryTherapistComment = this.generalTraumaHistoryTherapistCommentTxtBox.Value;
                ds.Contacts[0].GeneralTraumaHistorySupervisorComment = this.generalTraumaHistorySupervisorCommentTxtBox.Value;
                ds.Contacts[0].SpecificTraumaHistory = this.RetrieveSelect2ValueFromPageForm(this.specificTraumaHistoryDDL.ID);   //this.specificTraumaHistoryDDL.Value == null ? "" : this.specificTraumaHistoryDDL.Value;
                ds.Contacts[0].SpecificTraumaHistoryTherapistComment = this.specificTraumaHistoryTherapistCommentTxtBox.Value;
                ds.Contacts[0].SpecificTraumaHistorySupervisorComment = this.specificTraumaHistorySupervisorCommentTxtBox.Value;
                ds.Contacts[0].GeneralBiographicalInfo = this.generalBiographicalInfoTxtBox.Value;
                ds.Contacts[0].GeneralBiographicalInfoTherapistComment = this.generalBiographicalInfoTherapistCommentTxtBox.Value;
                ds.Contacts[0].GeneralBiographicalInfoSupervisorComment = this.generalBiographicalInfoSupervisorCommentTxtBox.Value;

                //4o tab - ClinicalView Information
                ds.Contacts[0].Developmental = this.RetrieveSelect2ValueFromPageForm(this.developmentalDDL.ID);  //this.developmentalDDL.Value == null ? "" : this.developmentalDDL.Value;
                ds.Contacts[0].DevelopmentalTherapistComment = this.developmentalTherapistCommentTxtBox.Value;
                ds.Contacts[0].DevelopmentalSupervisorComment = this.developmentalSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Neurodevelopmental = this.RetrieveSelect2ValueFromPageForm(this.neurodevelopmentalDDL.ID);   //this.neurodevelopmentalDDL.Value == null ? "" : this.neurodevelopmentalDDL.Value;
                //ds.Contacts[0].NeurodevelopmentalTherapistComment = this.neurodevelopmentalTherapistCommentTxtBox.Value;
                //ds.Contacts[0].NeurodevelopmentalSupervisorComment = this.neurodevelopmentalSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Learnings = this.RetrieveSelect2ValueFromPageForm(this.learningsDDL.ID);   //this.learningsDDL.Value == null ? "" : this.learningsDDL.Value;
                //ds.Contacts[0].LearningsTherapistComment = this.learningsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].LearningsSupervisorComment = this.learningsSupervisorCommentTxtBox.Value;
                ds.Contacts[0].EmotionalDifficulties = this.emotionalDifficultiesTxtBox.Value;
                ds.Contacts[0].EmotionalDifficultiesTherapistComment = this.emotionalDifficultiesTherapistCommentTxtBox.Value;
                ds.Contacts[0].EmotionalDifficultiesSupervisorComment = this.emotionalDifficultiesSupervisorCommentTxtBox.Value;
                ds.Contacts[0].EmotionalRemarks = this.emotionalRemarksTxtBox.Value;
                ds.Contacts[0].EmotionalRemarksTherapistComment = this.emotionalRemarksTherapistCommentTxtBox.Value;
                ds.Contacts[0].EmotionalRemarksSupervisorComment = this.emotionalRemarksSupervisorCommentTxtBox.Value;
                ds.Contacts[0].EatingDisorder = this.RetrieveSelect2ValueFromPageForm(this.eatingDisorderDDL.ID);
                ds.Contacts[0].EatingDisorderTherapistComment = this.eatingDisorderTherapistCommentTxtBox.Value;
                ds.Contacts[0].EatingDisorderSupervisorComment = this.eatingDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Anorexia = this.RetrieveSelect2ValueFromPageForm(this.anorexiaDDL.ID);   //this.anorexiaDDL.Value == null ? "" : this.anorexiaDDL.Value;
                //ds.Contacts[0].AnorexiaTherapistComment = this.anorexiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].AnorexiaSupervisorComment = this.anorexiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Bulimia = this.bulimiaTxtBox.Value;
                //ds.Contacts[0].BulimiaTherapistComment = this.bulimiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].BulimiaSupervisorComment = this.bulimiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Overeating = this.overeatingTxtBox.Value;
                //ds.Contacts[0].OvereatingTherapistComment = this.overeatingTherapistCommentTxtBox.Value;
                //ds.Contacts[0].OvereatingSupervisorComment = this.overeatingSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Osfed = this.osfedTxtBox.Value;
                //ds.Contacts[0].OsfedTherapistComment = this.osfedTherapistCommentTxtBox.Value;
                //ds.Contacts[0].OsfedSupervisorComment = this.osfedSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Allophagia = this.allophagiaTxtBox.Value;
                //ds.Contacts[0].AllophagiaTherapistComment = this.allophagiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].AllophagiaSupervisorComment = this.allophagiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].RuminationDisorder = this.ruminationDisorderTxtBox.Value;
                //ds.Contacts[0].RuminationDisorderTherapistComment = this.ruminationDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].RuminationDisorderSupervisorComment = this.ruminationDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].FoodIntakeDisorder = this.foodIntakeDisorderTxtbox.Value;
                //ds.Contacts[0].FoodIntakeDisorderTherapistComment = this.foodIntakeDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].FoodIntakeDisorderSupervisorComment = this.foodIntakeDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Orthorexia = this.orthorexiaTxtBox.Value;
                //ds.Contacts[0].OrthorexiaTherapistComment = this.orthorexiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].OrthorexiaSupervisorComment = this.orthorexiaSupervisorCommentTxtBox.Value;

                ds.Contacts[0].Moods = this.RetrieveSelect2ValueFromPageForm(this.moodsDDL.ID);   //this.majorDepressionDDL.Value == null ? "" : this.majorDepressionDDL.Value;
                ds.Contacts[0].MoodsTherapistComment = this.moodsTherapistCommentTxtBox.Value;
                ds.Contacts[0].MoodsSupervisorComment = this.moodsSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].MajorDepression = this.RetrieveSelect2ValueFromPageForm(this.majorDepressionDDL.ID);   //this.majorDepressionDDL.Value == null ? "" : this.majorDepressionDDL.Value;
                //ds.Contacts[0].MajorDepressionTherapistComment = this.majorDepressionTherapistCommentTxtBox.Value;
                //ds.Contacts[0].MajorDepressionSupervisorComment = this.majorDepressionSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Cyclothymia = this.cyclothymiaTxtBox.Value;
                //ds.Contacts[0].CyclothymiaTherapistComment = this.cyclothymiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].CyclothymiaSupervisorComment = this.cyclothymiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Dipolic = this.dipolicTxtBox.Value;
                //ds.Contacts[0].DipolicTherapistComment = this.dipolicTherapistCommentTxtBox.Value;
                //ds.Contacts[0].DipolicSupervisorComment = this.dipolicSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Dysthymia = this.RetrieveSelect2ValueFromPageForm(this.dysthymiaDDL.ID);   //this.dysthymiaDDL.Value == null ? "" : this.dysthymiaDDL.Value;
                //ds.Contacts[0].DysthymiaTherapistComment = this.dysthymiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].DysthymiaSupervisorComment = this.dysthymiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Melancholic = this.melancholicChkBox.Checked;
                //ds.Contacts[0].MelancholicTherapistComment = this.melancholicTherapistCommentTxtBox.Value;
                //ds.Contacts[0].MelancholicSupervisorComment = this.melancholicSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Catatonic = this.catatonicChkBox.Checked;
                //ds.Contacts[0].CatatonicTherapistComment = this.catatonicTherapistCommentTxtBox.Value;
                //ds.Contacts[0].CatatonicSupervisorComment = this.catatonicSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].TherapyResistant = this.therapyResistantChkBox.Checked;
                //ds.Contacts[0].******************************** = this.therapyResistantTherapistCommentTxtBox.Value;
                //ds.Contacts[0].TherapyResistantSupervisorComment = this.therapyResistantSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Regression = this.regressionChkBox.Checked;
                //ds.Contacts[0].RegressionTherapistComment = this.regressionTherapistCommentTxtBox.Value;
                //ds.Contacts[0].RegressionSupervisorComment = this.regressionSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Chronic = this.chronicChkBox.Checked;
                //ds.Contacts[0].ChronicTherapistComment = this.chronicTherapistCommentTxtBox.Value;
                //ds.Contacts[0].ChronicSupervisorComment = this.chronicSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Seasonal = this.seasonalChkBox.Checked;
                //ds.Contacts[0].SeasonalTherapistComment = this.seasonalTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SeasonalSupervisorComment = this.seasonalSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Epilochia = this.epilochiaChkBox.Checked;
                //ds.Contacts[0].EpilochiaTherapistComment = this.epilochiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].EpilochiaSupervisorComment = this.epilochiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Pregnancy = this.pregnancyChkBox.Checked;
                //ds.Contacts[0].PregnancyTherapistComment = this.pregnancyTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PregnancySupervisorComment = this.pregnancySupervisorCommentTxtBox.Value;
                //ds.Contacts[0].ThirdAge = this.RetrieveSelect2ValueFromPageForm(this.thirdAgeDDL.ID);   //this.thirdAgeDDL.Value == null ? "" : this.thirdAgeDDL.Value;
                //ds.Contacts[0].ThirdAgeTherapistComment = this.thirdAgeTherapistCommentTxtBox.Value;
                //ds.Contacts[0].ThirdAgeSupervisorComment = this.thirdAgeSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Informal = this.RetrieveSelect2ValueFromPageForm(this.informalDDL.ID); //this.informalDDL.Value == null ? "" : this.informalDDL.Value;
                //ds.Contacts[0].InformalTherapistComment = this.informalTherapistCommentTxtBox.Value;
                //ds.Contacts[0].InformalSupervisorComment = this.informalSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PsychoticDepression = this.RetrieveSelect2ValueFromPageForm(this.psychoticDepressionDDL.ID);   //this.psychoticDepressionDDL.Value == null ? "" : this.psychoticDepressionDDL.Value;
                //ds.Contacts[0].PsychoticDepressionTherapistComment = this.psychoticDepressionTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PsychoticDepressionSupervisorComment = this.psychoticDepressionSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].ElassonDepressiveDisorder = this.elassonDepressiveDisorderChkBox.Checked;
                //ds.Contacts[0].ElassonDepressiveDisorderTherapistComment = this.elassonDepressiveDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].ElassonDepressiveDisorderSupervisorComment = this.elassonDepressiveDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].ShortIntermmitentForm = this.shortIntermmitentFormChkBox.Checked;
                //ds.Contacts[0].ShortIntermmitentFormTherapistComment = this.shortIntermmitentFormTherapistCommentTxtBox.Value;
                //ds.Contacts[0].ShortIntermmitentFormSupervisorComment = this.shortIntermmitentFormSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PremenstrualDysphoricDisorder = this.premenstrualDysphoricDisorderChkBox.Checked;
                //ds.Contacts[0].PremenstrualDysphoricDisorderTherapistComment = this.premenstrualDysphoricDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PremenstrualDysphoricDisorderSupervisorComment = this.premenstrualDysphoricDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].DepressivePseudonoia = this.depressivePseudonoiaChkBox.Checked;
                //ds.Contacts[0].DepressivePseudonoiaTherapistComment = this.depressivePseudonoiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].DepressivePseudonoiaSupervisorComment = this.depressivePseudonoiaSupervisorCommentTxtBox.Value;
                ds.Contacts[0].OtherMoodObservations = this.otherMoodObservationsTxtBox.Value;
                ds.Contacts[0].OtherMoodObservationsTherapistComment = this.otherMoodObservationsTherapistCommentTxtBox.Value;
                ds.Contacts[0].OtherMoodObservationsSupervisorComment = this.otherMoodObservationsSupervisorCommentTxtBox.Value;

                ds.Contacts[0].Anxiety = this.RetrieveSelect2ValueFromPageForm(this.anxietyDDL.ID);
                ds.Contacts[0].AnxietyTherapistComment = this.anxietyTherapistCommentTxtBox.Value;
                ds.Contacts[0].AnxietySupervisorComment = this.anxietySupervisorCommentTxtBox.Value;
                //ds.Contacts[0].SpecificFears = this.specificFearsTxtBox.Value;
                //ds.Contacts[0].SpecificFearsTherapistComment = this.specificFearsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SpecificFearsSupervisorComment = this.specificFearsSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].SocialPhobia = this.socialPhobiaTxtBox.Value;
                //ds.Contacts[0].SocialPhobiaTherapistComment = this.socialPhobiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SocialPhobiaSupervisorComment = this.socialPhobiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PanicDisorder = this.panicDisorderTxtBox.Value;
                //ds.Contacts[0].PanicDisorderTherapistComment = this.panicDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PanicDisorderSupervisorComment = this.panicDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Agoraphobia = this.agoraphobiaTxtBox.Value;
                //ds.Contacts[0].AgoraphobiaTherapistComment = this.agoraphobiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].AgoraphobiaSupervisorComment = this.agoraphobiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].AnxietyPhysicalSymptoms = this.RetrieveSelect2ValueFromPageForm(this.anxietyPhysicalSymptomsDDL.ID);   //this.anxietyPhysicalSymptomsDDL.Value == null ? "" : this.anxietyPhysicalSymptomsDDL.Value;
                //ds.Contacts[0].AnxietyPhysicalSymptomsTherapistComment = this.anxietyPhysicalSymptomsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].AnxietyPhysicalSymptomsSupervisorComment = this.anxietyPhysicalSymptomsSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].GeneralizedAxiety = this.generalizedAxietyChkBox.Checked;
                //ds.Contacts[0].GeneralizedAxietyTherapistComment = this.generalizedAxietyTherapistCommentTxtBox.Value;
                //ds.Contacts[0].GeneralizedAxietySupervisorComment = this.generalizedAxietySupervisorCommentTxtBox.Value;
                //ds.Contacts[0].ObsessiveIdeas = this.RetrieveSelect2ValueFromPageForm(this.obsessiveIdeasDDL.ID);  //this.obsessiveIdeasDDL.Value == null ? "" : this.obsessiveIdeasDDL.Value;
                //ds.Contacts[0].ObsessiveIdeasTherapistComment = this.obsessiveIdeasTherapistCommentTxtBox.Value;
                //ds.Contacts[0].ObsessiveIdeasSupervisorComment = this.obsessiveIdeasSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].IdeoPsychoComplusions = this.RetrieveSelect2ValueFromPageForm(this.ideoPsychoComplusionsDDL.ID);  // this.ideoPsychoComplusionsDDL.Value == null ? "" : this.ideoPsychoComplusionsDDL.Value;
                //ds.Contacts[0].IdeoPsychoComplusionsTherapistComment = this.ideoPsychoComplusionsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].IdeoPsychoComplusionsSupervisorComment = this.ideoPsychoComplusionsSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Tik = this.tikChkBox.Checked;
                //ds.Contacts[0].TikTherapistComment = this.tikTherapistCommentTxtBox.Value;
                //ds.Contacts[0].TikSupervisorComment = this.tikSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Dysmorphobia = this.RetrieveSelect2ValueFromPageForm(this.dysmorphobiaDDL.ID);   //this.dysmorphobiaDDL.Value == null ? "" : this.dysmorphobiaDDL.Value;
                //ds.Contacts[0].DysmorphobiaTherapistComment = this.dysmorphobiaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].DysmorphobiaSupervisorComment = this.dysmorphobiaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].ParacumulationDisorder = this.paracumulationDisorderChkBox.Checked;
                //ds.Contacts[0].ParacumulationDisorderTherapistComment = this.paracumulationDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].ParacumulationDisorderSupervisorComment = this.paracumulationDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Trichotillomania = this.trichotillomaniaChkBox.Checked;
                //ds.Contacts[0].******************************** = this.trichotillomaniaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].TrichotillomaniaSupervisorComment = this.trichotillomaniaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PostTraumaticStress = this.postTraumaticStressTxtBox.Value;
                //ds.Contacts[0].PostTraumaticStressTherapistComment = this.postTraumaticStressTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PostTraumaticStressSupervisorComment = this.postTraumaticStressSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].OtherStressObservations = this.otherStressObservationsTxtBox.Value;
                //ds.Contacts[0].OtherStressObservationsTherapistComment = this.otherStressObservationsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].OtherStressObservationsSupervisorComment = this.otherStressObservationsSupervisorCommentTxtBox.Value;

                ds.Contacts[0].Sleep = this.RetrieveSelect2ValueFromPageForm(this.sleepDDL.ID);   //this.insomniaDDL.Value == null ? "" : this.insomniaDDL.Value;
                ds.Contacts[0].SleepTherapistComment = this.sleepTherapistCommentTxtBox.Value;
                ds.Contacts[0].SleepSupervisorComment = this.sleepSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Insomnia = this.RetrieveSelect2ValueFromPageForm(this.insomniaDDL.ID);   //this.insomniaDDL.Value == null ? "" : this.insomniaDDL.Value;
                //ds.Contacts[0].InsomniaTherapistComment = this.insomniaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].InsomniaSupervisorComment = this.insomniaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].SleepDisorder = this.RetrieveSelect2ValueFromPageForm(this.sleepDisorderDDL.ID);   //this.sleepDisorderDDL.Value == null ? "" : this.sleepDisorderDDL.Value;
                //ds.Contacts[0].SleepDisorderTherapistComment = this.sleepDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SleepDisorderSupervisorComment = this.sleepDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].OtherSleepObservations = this.otherSleepObservationsTxtBox.Value;
                //ds.Contacts[0].OtherSleepObservationsTherapistComment = this.otherSleepObservationsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].OtherSleepObservationsSupervisorComment = this.otherSleepObservationsSupervisorCommentTxtBox.Value;

                ds.Contacts[0].Psychotic = this.RetrieveSelect2ValueFromPageForm(this.psychoticDDL.ID);
                ds.Contacts[0].PsychoticTherapistComment = this.psychoticTherapistCommentTxtBox.Value;
                ds.Contacts[0].PsychoticSupervisorComment = this.psychoticSupervisorCommentTxtBox.Value;
                ds.Contacts[0].DefenseMechanisms = this.RetrieveSelect2ValueFromPageForm(this.defenseMechanismsDDL.ID);
                ds.Contacts[0].DefenseMechanismsTherapistComment = this.defenseMechanismsTherapistCommentTxtBox.Value;
                ds.Contacts[0].DefenseMechanismsSupervisorComment = this.defenseMechanismsSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Shapes = this.RetrieveSelect2ValueFromPageForm(this.shapesDDL.ID);
                ds.Contacts[0].ShapesTherapistComment = this.shapesTherapistCommentTxtBox.Value;
                ds.Contacts[0].ShapesSupervisorComment = this.shapesSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PhychoticSymptoms = this.phychoticSymptomsTxtBox.Value;
                //ds.Contacts[0].PhychoticSymptomsTherapistComment = this.phychoticSymptomsTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PhychoticSymptomsSupervisorComment = this.phychoticSymptomsSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Schizophrenia = this.schizophreniaTxtBox.Value;
                //ds.Contacts[0].SchizophreniaTherapistComment = this.schizophreniaTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SchizophreniaSupervisorComment = this.schizophreniaSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Schizoemotional = this.schizoemotionalTxtBox.Value;
                //ds.Contacts[0].SchizoemotionalTherapistComment = this.schizoemotionalTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SchizoemotionalSupervisorComment = this.schizoemotionalSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Delirusive = this.delirusiveTxtBox.Value;
                //ds.Contacts[0].DelirusiveTherapistComment = this.delirusiveTherapistCommentTxtBox.Value;
                //ds.Contacts[0].DelirusiveSupervisorComment = this.delirusiveSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].SortPshychotic = this.sortPshychoticTxtBox.Value;
                //ds.Contacts[0].SortPshychoticTherapistComment = this.sortPshychoticTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SortPshychoticSupervisorComment = this.sortPshychoticSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].OrganicPhsychosyndrome = this.organicPhsychosyndromeTxtBox.Value;
                //ds.Contacts[0].OrganicPhsychosyndromeTherapistComment = this.organicPhsychosyndromeTherapistCommentTxtBox.Value;
                //ds.Contacts[0].OrganicPhsychosyndromeSupervisorComment = this.organicPhsychosyndromeSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].Schizophrenicoform = this.schizophrenicoformTxtBox.Value;
                //ds.Contacts[0].SchizophrenicoformTherapistComment = this.schizophrenicoformTherapistCommentTxtBox.Value;
                //ds.Contacts[0].SchizophrenicoformSupervisorComment = this.schizophrenicoformSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PersistentDisorder = this.persistentDisorderTxtBox.Value;
                //ds.Contacts[0].PersistentDisorderTherapistComment = this.persistentDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PersistentDisorderSupervisorComment = this.persistentDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].PhysicalConditionDisorder = this.physicalConditionDisorderTxtBox.Value;
                //ds.Contacts[0].PhysicalConditionDisorderTherapistComment = this.physicalConditionDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].PhysicalConditionDisorderSupervisorComment = this.physicalConditionDisorderSupervisorCommentTxtBox.Value;
                //ds.Contacts[0].NonSpecifiedDisorder = this.nonSpecifiedDisorderTxtBox.Value;
                //ds.Contacts[0].NonSpecifiedDisorderTherapistComment = this.nonSpecifiedDisorderTherapistCommentTxtBox.Value;
                //ds.Contacts[0].NonSpecifiedDisorderSupervisorComment = this.nonSpecifiedDisorderSupervisorCommentTxtBox.Value;

                ds.Contacts[0].Paranoid = this.RetrieveSelect2ValueFromPageForm(this.paranoidDDL.ID);   //this.paranoidDDL.Value == null ? "" : this.paranoidDDL.Value;
                ds.Contacts[0].ParanoidTherapistComment = this.paranoidTherapistCommentTxtBox.Value;
                ds.Contacts[0].ParanoidSupervisorComment = this.paranoidSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Schizoid = this.RetrieveSelect2ValueFromPageForm(this.schizoidDDL.ID);   //this.schizoidDDL.Value == null ? "" : this.schizoidDDL.Value;
                ds.Contacts[0].SchizoidTherapistComment = this.schizoidTherapistCommentTxtBox.Value;
                ds.Contacts[0].SchizoidSupervisorComment = this.schizoidSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Schizotype = this.RetrieveSelect2ValueFromPageForm(this.schizotypeDDL.ID);   //this.schizotypeDDL.Value == null ? "" : this.schizotypeDDL.Value;
                ds.Contacts[0].SchizotypeTherapistComment = this.schizotypeTherapistCommentTxtBox.Value;
                ds.Contacts[0].SchizotypeSupervisorComment = this.schizotypeSupervisorCommentTxtBox.Value;
                ds.Contacts[0].OnLimit = this.RetrieveSelect2ValueFromPageForm(this.onLimitDDL.ID);   //this.onLimitDDL.Value == null ? "" : this.onLimitDDL.Value;
                ds.Contacts[0].OnLimitTherapistComment = this.onLimitTherapistCommentTxtBox.Value;
                ds.Contacts[0].OnLimitSupervisorComment = this.onLimitSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Antisocial = this.RetrieveSelect2ValueFromPageForm(this.antisocialDDL.ID);   //this.antisocialDDL.Value == null ? "" : this.antisocialDDL.Value;
                ds.Contacts[0].AntisocialTherapistComment = this.antisocialTherapistCommentTxtBox.Value;
                ds.Contacts[0].AntisocialSupervisorComment = this.antisocialSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Histronic = this.RetrieveSelect2ValueFromPageForm(this.histronicDDL.ID);   //this.histronicDDL.Value == null ? "" : this.histronicDDL.Value;
                ds.Contacts[0].HistronicTherapistComment = this.histronicTherapistCommentTxtBox.Value;
                ds.Contacts[0].HistronicSupervisorComment = this.histronicSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Narcissistic = this.RetrieveSelect2ValueFromPageForm(this.narcissisticDDL.ID);   //this.narcissisticDDL.Value == null ? "" : this.narcissisticDDL.Value;
                ds.Contacts[0].NarcissisticTherapistComment = this.narcissisticTherapistCommentTxtBox.Value;
                ds.Contacts[0].NarcissisticSupervisorComment = this.narcissisticSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Ideopsychocompression = this.RetrieveSelect2ValueFromPageForm(this.ideopsychocompressionDDL.ID);   //this.ideopsychocompressionDDL.Value == null ? "" : this.ideopsychocompressionDDL.Value;
                ds.Contacts[0].IdeopsychocompressionTherapistComment = this.ideopsychocompressionTherapistCommentTxtBox.Value;
                ds.Contacts[0].IdeopsychocompressionSupervisorComment = this.ideopsychocompressionSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Avoidable = this.RetrieveSelect2ValueFromPageForm(this.avoidableDDL.ID);   //this.avoidableDDL.Value == null ? "" : this.avoidableDDL.Value;
                ds.Contacts[0].AvoidableTherapistComment = this.avoidableTherapistCommentTxtBox.Value;
                ds.Contacts[0].AvoidableSupervisorComment = this.avoidableSupervisorCommentTxtBox.Value;
                ds.Contacts[0].Addictive = this.RetrieveSelect2ValueFromPageForm(this.addictiveDDL.ID);  //this.addictiveDDL.Value == null ? "" : this.addictiveDDL.Value;
                ds.Contacts[0].AddictiveTherapistComment = this.addictiveTherapistCommentTxtBox.Value;
                ds.Contacts[0].AddictiveSupervisorComment = this.addictiveSupervisorCommentTxtBox.Value;
                ds.Contacts[0].PassiveAggressive = this.RetrieveSelect2ValueFromPageForm(this.passiveAggressiveDDL.ID);  //this.passiveAggressiveDDL.Value == null ? "" : this.passiveAggressiveDDL.Value;
                ds.Contacts[0].PassiveAggressiveTherapistComment = this.passiveAggressiveTherapistCommentTxtBox.Value;
                ds.Contacts[0].PassiveAggressiveSupervisorComment = this.passiveAggressiveSupervisorCommentTxtBox.Value;
                ds.Contacts[0].OtherDisorderInfo = this.otherDisorderInfoTxtBox.Value;
                ds.Contacts[0].OtherDisorderInfoTherapistComment = this.otherDisorderInfoTherapistCommentTxtBox.Value;
                ds.Contacts[0].OtherDisorderInfoSupervisorComment = this.otherDisorderInfoSupervisorCommentTxtBox.Value;

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        /// <summary>
        /// Searches the Request.Form for the specified control ID and gets the value.
        /// </summary>
        /// <param name="controlId"></param>
        /// <returns></returns>
        private string RetrieveControlValueFromPageForm(string controlId)
        {
            try
            {
                foreach (string key in Request.Form.AllKeys)
                {
                    if (key.EndsWith(controlId) == true)
                    {
                        return Request.Form[key];
                    }
                }

                return "";

                ////Αν βρήκε το control με το controlId στο Request.Form. Αν δεν το βρήκε σημαίνει ότι το select2 δεν έχει τιμή.
                //if (Request.Form.AllKeys.Where(n => n.EndsWith(controlId)).Count() > 0)
                //{
                //    List<string> values = new List<string>();
                //    Request.Form.AllKeys.Where(n => n.EndsWith(controlId)).ToList().ForEach(x => values.Add(Request.Form[x]));

                //    return values[0];
                //}
                //else
                //{
                //    return "";
                //}
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(new ApplicationException("controlId: " + controlId));
                throw exp;
            }
        }

        private string RetrieveSelect2ValueFromPageForm(string controlName)
        {
            try
            {
                controlName = "$" + controlName;  //Βάζουμε το $ μπροστά γιατί όλα τα select2 είναι σε container controls και έχουν κι άλλο όνομα μπροστά.
                return this.RetrieveControlValueFromPageForm(controlName).Replace(",", "|");
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(new ApplicationException("controlId: " + controlName));
                throw exp;
            }
        }

        protected void saveCloseBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(true);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void SetValueOnSelectControl(HtmlSelect selectControl, string value)
        {
            //Καθαρίζει τα selected
            foreach (System.Web.UI.WebControls.ListItem item in selectControl.Items)
            {
                item.Selected = false;
            }

            if (value != null && value.Trim() != "")
            {
                //Κάνει select αυτά που πρέπει
                string[] values = value.Split('|');

                foreach (string s in values)
                {
                    try
                    {
                        selectControl.Items.FindByValue(s).Selected = true;
                    }
                    catch (Exception exp)
                    {
                        Data.ExceptionLogger.LogException(new Exception("Exception in control " + selectControl.ID + ". Value:" + s, exp));
                    }
                }
            }
        }

        private void Save(bool close)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                ds.EnforceConstraints = false;

                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (this.ValidateData(ds.Contacts[0]) == true)
                {
                    Data.Business.SaveAllData(ds);
                    ViewState["Contact"] = ds;

                    if (close)
                    {
                        Session.Remove("SelectedQuestionnaireId");
                        Response.Redirect(@"~\Contacts.aspx");
                    }
                    else
                    {
                        this.SetDataOnUIControls(ds);
                    }
                }
                else
                {
                    this.SetDataOnUIControls(ds);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void saveBtn_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save(false);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void closeBtn_Click(object sender, EventArgs e)
        {
            ViewState.Remove("Contact");
            Session.Remove("SelectedQuestionnaireId");
            Response.Redirect(@"Contacts.aspx");
        }

        protected void deleteBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];

                //if (ds.Contacts[0].RowState != System.Data.DataRowState.Added)
                //{
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete");
                //}
                //else
                //{
                //    Session.Remove("Contact");
                //    Response.Redirect(@"Contacts.aspx");
                //}

                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private void Delete()
        {
            Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Contact"];

            ds.Contacts[0].Delete();
            Data.Business.SaveAllData(ds);

            Response.Redirect(@"Contacts.aspx");
        }

        private void ServerMessageButtonClicked(object sender, ButtonClickedArgs args)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (args.Action == "Delete")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        this.Delete();
                    }
                }
                else if (args.Action == "SessionExpired")
                {
                    Response.Redirect("Default.aspx");
                }
                else if (args.Action == "DeleteQuestionnaire")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 questionnaireId = Convert.ToInt64(args.Tag);

                        //Αν το Questionnaire υπάρχει.
                        if (ds.Questionnaires.FindByQuestionnaireId(questionnaireId) != null)
                        {
                            ds.Questionnaires.FindByQuestionnaireId(questionnaireId).Delete();
                        }

                    }
                    //this.contactTab.SelectedItemIndex = this.contactTab.Items.IndexOf(this.questionnairesTab);  //Εμφανίζει το tab
                    this.selectedTab.Value = "questionnairesTab";
                }
                else if (args.Action == "DeleteContactEmailTemplate")
                {
                    if (args.ButtonClicked == ButtonClicked.Yes)
                    {
                        Int64 contactEmailTemplateId = Convert.ToInt64(args.Tag);

                        //Αν το ContactEmailTemplate υπάρχει.
                        if (ds.ContactEmailTemplates.FindByContactEmailTemplateId(contactEmailTemplateId) != null)
                        {
                            ds.ContactEmailTemplates.FindByContactEmailTemplateId(contactEmailTemplateId).Delete();
                        }
                    }
                }

                ViewState["Contact"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private bool ValidateData(Data.MentalViewDataSet.ContactsRow contactsRow)
        {
            try
            {
                //Αν το πεδίο LastName δεν είναι συμπληρωμένο
                if (contactsRow.LastName == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactFullNameRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν το πεδίο FirstName δεν είναι συμπληρωμένο
                if (contactsRow.FirstName == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactFullNameRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν το πεδίο Mobile1 δεν είναι συμπληρωμένο
                if (contactsRow.Mobile1 == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactMobile1RequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }

                //Αν το πεδίο Email δεν είναι συμπληρωμένο
                if (contactsRow.Email == "")
                {
                    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("ContactEmailRequiredMessage").ToString(), ServerMessageButtons.Ok, "");
                    return false;
                }


                return true;
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                throw;
            }
        }

        protected void newQuestionnaireBtn_Click(object sender, EventArgs e)
        {
            //try
            //{
            //    Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
            //    this.GetDataFromUIControls(ref ds);
            //    ViewState["Contact"] = ds;

            //    string selectedQuestionnaire = Request.Form.Get("ctl00$ctl00$includeFilesBody$mainBody$newQuestionnaire2DDL").ToString();

            //    if (selectedQuestionnaire != null && selectedQuestionnaire != "")
            //    {
            //        DataTable questionnairesDT;
            //        questionnairesDT = (DataTable)this.newQuestionnaire2DDL.DataSource;
            //        MentalViewDataSet.QuestionnairesRow newQuestionnairesRow = ds.Questionnaires.NewQuestionnairesRow();
            //        newQuestionnairesRow.ContactId = ds.Contacts[0].ContactId;
            //        newQuestionnairesRow.Name = questionnairesDT.Select(@"Value='" + selectedQuestionnaire + @"'")[0]["Text"].ToString();
            //        newQuestionnairesRow.CreateDate = DateTime.Now;
            //        ds.Questionnaires.AddQuestionnairesRow(newQuestionnairesRow);

            //        #region  Προσθέτει τις ερωτήσεις
            //        if (selectedQuestionnaire == "LifeQuality")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "qol σωματική κατάσταση", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "qol κοινωνικές επαφές", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "qol περιβάλλον και πρόσβαση στην πληροφορία", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "qol ψυχολογία", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο ποιότητα ζωής", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "AnxietyDepressionPanic")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "social r scale", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "beck anx", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "hads", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "panic atack", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "beck dep", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "social anx", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "panic atack", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με το άγχος", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με κατάθλιψη", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με φοβίες", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "EmotionalDevelopment")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "adhd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "alexithimia", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "e.i 30", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "e.i 20", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με αναπτυξιακά", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "Background")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "problem list", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "family frame", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο οικογενειακών ζητημάτων", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "Shapes")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ ED Συναισθηματική Στέρηση", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ AB Εγκατάλειψη/Αστάθεια", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ MA Καχυποψία κακοποίηση", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ SI Κοινωνική αποξένωση", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ DS Ελλατωματικότητα ντροπή", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ FA Αποτυχία", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ DI Εξάρτηση ανικανότητα", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ VU Ευαλωτότητα σε βλάβη", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ EU Υπερβολική εμπλοκή", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ SB Υποτακτικότητα", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ SS Aυτοθυσία", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ EI Συναισθηματική αναστολή", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ US Ανελαστικά πρότυπα", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ ET Αυτονόητο δικαίωμα", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ IS Ανεπαρκής αυτοέλεγχος αυτοπειθαρχία", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ AS Αναζήτηση επιδοκιμασίας αναγνώρισης", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ NP Αρνητικότητα απαισιοδοξία", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "QST YSQ PU Τιμωρητικότητα", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με τα σχήματα", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "DefenceMechanisms")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "problem list", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "family frame", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο οικογενειακών ζητημάτων", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "PersonalityNine")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "ennea 1", "", "Σειρά Προτεραιότητας 1 Έως 9" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "ennea 2", "", "Επιλογή από 1 έως 9" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "ennea institute 144", "", "Σειρά Προτεραιότητας 1 Έως 9" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με την προσωπικότητα", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "P-PathologicalMoodChanges")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "bd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "mania/bd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο διπολικού φάσματος", "", "" }, false);
            //        }
            //        else if (selectedQuestionnaire == "P-PathologicalPersonnality")
            //        {
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "sczpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "sctpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "ppd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "bpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "npd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "hpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "anpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "avpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "ocpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "dpd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "papd", "", "" }, false);
            //            ds.QuestionnaireQuestions.LoadDataRow(new object[] { null, newQuestionnairesRow.QuestionnaireId, "Άλλο ερωτηματολόγιο σχετιζόμενο με τις διαταραχές προσωπικότητας", "", "" }, false);
            //        }
            //        #endregion

            //        //Business.SaveAllData(ds);

            //        this.newQuestionnaire2DDL.Value = "";
            //    }

            //    ViewState["Contact"] = ds;
            //    this.SetDataOnUIControls(ds);
            //    //this.contactTab.SelectedItemIndex = this.contactTab.Items.IndexOf(this.questionnairesTab);  //Εμφανίζει το tab
            //    this.selectedTab.Value = "questionnairesTab";
            //}
            //catch (Exception exp)
            //{
            //    Data.ExceptionLogger.LogException(exp);
            //    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            //}
        }

        protected void questionnairiesGrid_ServerRecordDoubleClick(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (e.EventType == "recordDoubleClick")
                {
                    string contactId = ((Dictionary<string, object>)e.Arguments["data"])["ContactId"].ToString();
                    Response.Redirect("~/Contact.aspx?ContactId=" + contactId);
                }

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void questionnairiesGrid_ServerCommandButtonClick(object sender, GridEventArgs e)
        {
            //try
            //{
            //    Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Contact"];
            //    this.GetDataFromUIControls(ref ds);
            //    ViewState["Contact"] = ds;

            //    if (e.EventType == "commandButtonClick")
            //    {
            //        if (e.Arguments["commandType"].ToString() == "Edit")
            //        {
            //            string contactId = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireId"].ToString();
            //            Response.Redirect("~/Contact.aspx?ContactId=" + contactId);
            //        }
            //        else if (e.Arguments["commandType"].ToString() == "Delete")
            //        {
            //            string questionnaireId = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireId"].ToString();
            //            ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", questionnaireId);
            //        }
            //        else if (e.Arguments["commandType"].ToString() == "EditQuestions")
            //        {
            //            string questionnaireIdText = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireId"].ToString();

            //            this.GetDataFromUIControls(ref ds);
            //            ViewState["Contact"] = ds;

            //            Int64 questionnaireId = Convert.ToInt64(questionnaireIdText);
            //            Session["SelectedQuestionnaireId"] = questionnaireId;
            //        }
            //    }

            //    this.SetDataOnUIControls(ds);
            //    //this.contactTab.SelectedItemIndex = this.contactTab.Items.IndexOf(this.questionnairesTab);  //Εμφανίζει το tab
            //    this.selectedTab.Value = "questionnairesTab";
            //}
            //catch (Exception exp)
            //{
            //    if (exp.GetType() != typeof(ThreadAbortException))
            //    {
            //        Data.ExceptionLogger.LogException(exp);
            //        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            //    }
            //}
        }

        protected void questionnaireQuestionsGrid_ServerEditRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;


                Int64 questionnaireQuestionId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireQuestionId"]);
                MentalViewDataSet.QuestionnaireQuestionsRow questionnaireQuestionsRow = ds.QuestionnaireQuestions.FindByQuestionnaireQuestionId(questionnaireQuestionId);
                questionnaireQuestionsRow.Value = ((Dictionary<string, object>)e.Arguments["data"])["Value"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Value"].ToString();
                questionnaireQuestionsRow.Help = ((Dictionary<string, object>)e.Arguments["data"])["Help"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Help"].ToString();

                this.SetDataOnUIControls(ds);

                //this.contactTab.SelectedItemIndex = this.contactTab.Items.IndexOf(this.questionnairesTab);  //Εμφανίζει το tab
                this.selectedTab.Value = "questionnairesTab";

                if (Session["SelectedQuestionnaireId"] != null)
                {
                    Int64 questionnaireId = Convert.ToInt64(Session["SelectedQuestionnaireId"]);
                    this.questionnaireQuestionsGrid.DataSource = new DataView(ds.QuestionnaireQuestions, "QuestionnaireId=" + questionnaireId.ToString(), "", DataViewRowState.CurrentRows).ToTable();
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void questionnairiesGrid_ServerDeleteRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                string questionnaireId = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireId"].ToString();
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "DeleteQuestionnaire", questionnaireId);

                //ViewState["Contact"] = ds;
                this.SetDataOnUIControls(ds);

                //this.contactTab.SelectedItemIndex = this.contactTab.Items.IndexOf(this.questionnairesTab);  //Εμφανίζει το tab
                this.selectedTab.Value = "questionnairesTab";
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void appointmentsGrid_ServerCommandButtonClick(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "Edit")
                    {
                        string appointmentId = ((Dictionary<string, object>)e.Arguments["data"])["AppointmentId"].ToString();
                        Response.Redirect("~/Appointment.aspx?AppointmentId=" + appointmentId);
                    }
                    //else if (e.Arguments["commandType"].ToString() == "Delete")
                    //{
                    //    string taskId = ((Dictionary<string, object>)e.Arguments["data"])["TaskId"].ToString();
                    //    ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "Delete", taskId);
                    //}
                }

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #region  Reports
        //private WordDocument PrepareContactReport()
        //{
        //    try
        //    {
        //        Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
        //        this.GetDataFromUIControls(ref ds);

        //        WordDocument document = new Reporting.ReportsFactory().PrepareContactReport(ds);
        //        return document;
        //    }
        //    catch (Exception exp)
        //    {
        //        Data.ExceptionLogger.LogException(exp);
        //        throw;
        //    }
        //}

        protected void previewContactReportBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (this.ValidateData(ds.Contacts[0]))
                {
                    //Loads an existing Word document
                    //WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReport(ds, tenantId, this.printContactDataChkBox.Checked, this.printContactQuestionnairesChkBox.Checked, printContactAppointmentsChkBox.Checked);
                    WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReportNew(ds, tenantId, this.printContactDataChkBox.Checked, this.printContactQuestionnairesChkBox.Checked, printContactAppointmentsChkBox.Checked);

                    Session["Report"] = wordDocument;
                    Session["ReportName"] = Resources.GlobalResources.ContactReportText + " " + ds.Contacts[0].ContactId.ToString() + ".pdf";

                    ClientScript.RegisterStartupScript(this.GetType(), "OpenWin", @"window.open('/Reporting/ExportWordReport.aspx?Inline=true','_blank');", true);
                }

                this.SetDataOnUIControls(ds);

            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportContactReportToPdfBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (this.ValidateData(ds.Contacts[0]))
                {
                    //Loads an existing Word document
                    //WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReport(ds, tenantId, this.printContactDataChkBox.Checked, this.printContactQuestionnairesChkBox.Checked, this.printContactAppointmentsChkBox.Checked);
                    WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReportNew(ds, tenantId, true, this.printContactQuestionnairesChkBox.Checked, this.printContactAppointmentsChkBox.Checked);
                    //Initializes the ChartToImageConverter for converting charts during Word to pdf conversion
                    //wordDocument.ChartToImageConverter =  new ChartToImageConverter();
                    //Creates an instance of the DocToPDFConverter
                    DocToPDFConverter converter = new DocToPDFConverter();
                    //Converts Word document into PDF document
                    PdfDocument pdfDocument = converter.ConvertToPDF(wordDocument);
                    //Saves the PDF file 
                    //pdfDocument.Save("WordtoPDF.pdf");
                    MemoryStream stream = new MemoryStream();
                    //Saves the document to t
                    pdfDocument.Save(Resources.GlobalResources.ContactReportText + " " + ds.Contacts[0].ContactId.ToString() + ".pdf", Response, HttpReadType.Save);
                    //Closes the instance of document objects
                    pdfDocument.Close(true);
                    wordDocument.Close();
                }
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void exportContactReportToWordBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (this.ValidateData(ds.Contacts[0]))
                {
                    //Loads an existing Word document
                    //WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReport(ds, tenantId, this.printContactDataChkBox.Checked, this.printContactQuestionnairesChkBox.Checked, this.printContactAppointmentsChkBox.Checked);
                    WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReportNew(ds, tenantId, true, this.printContactQuestionnairesChkBox.Checked, this.printContactAppointmentsChkBox.Checked);

                    MemoryStream stream = new MemoryStream();
                    //Saves the document to Response
                    wordDocument.Save(Resources.GlobalResources.ContactReportText + " " + ds.Contacts[0].ContactId.ToString() + ".docx", FormatType.Docx, Response, HttpContentDisposition.InBrowser);
                    //Closes the instance of document objects
                    //wordDocument.Close();
                    wordDocument.Close();
                }
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        #endregion

        protected void newQuestionnaire2DDL_SelectedIndexChanged(object sender, EventArgs e)
        {
            int i = 0;
        }

        protected void questionnairiesGrid_ServerEditRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                Int64 questionnaireId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["previousData"])["QuestionnaireId"]);
                MentalViewDataSet.QuestionnairesRow questionnairesRow = ds.Questionnaires.FindByQuestionnaireId(questionnaireId);
                questionnairesRow.Name = ((Dictionary<string, object>)e.Arguments["data"])["Name"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Name"].ToString();
                if (((Dictionary<string, object>)e.Arguments["data"])["CreateDate"] != null)
                {
                    questionnairesRow.CreateDate = DateTime.Parse(((Dictionary<string, object>)e.Arguments["data"])["CreateDate"].ToString());
                }
                questionnairesRow.QuestionnaireInput = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireInput"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireInput"].ToString();
                questionnairesRow.QuestionnaireCode = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireCode"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireCode"].ToString();

                this.SetDataOnUIControls(ds);

                this.selectedTab.Value = "questionnairesTab";
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void questionnairiesGrid_ServerAddRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                //Int64 questionnaireId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["previousData"])["QuestionnaireId"]);
                MentalViewDataSet.QuestionnairesRow questionnairesRow = ds.Questionnaires.NewQuestionnairesRow();
                questionnairesRow.ContactId = ds.Contacts[0].ContactId;
                questionnairesRow.Name = ((Dictionary<string, object>)e.Arguments["data"])["Name"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Name"].ToString();
                if (((Dictionary<string, object>)e.Arguments["data"])["CreateDate"] != null)
                {
                    questionnairesRow.CreateDate = DateTime.Parse(((Dictionary<string, object>)e.Arguments["data"])["CreateDate"].ToString());
                }
                questionnairesRow.QuestionnaireInput = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireInput"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireInput"].ToString();
                questionnairesRow.QuestionnaireCode = ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireCode"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["QuestionnaireCode"].ToString();
                ds.Questionnaires.AddQuestionnairesRow(questionnairesRow);
                ViewState["Contact"] = ds;

                this.SetDataOnUIControls(ds);

                this.selectedTab.Value = "questionnairesTab";
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void sendEmailToContactBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (this.emailTemplatesDDL.SelectedIndices.Count > 0)
                {
                    if (ds.Contacts[0].Email.Trim() != "" && ds.Contacts[0].Email2.Trim() != "")
                    {
                        if (EmailManager.IsValidEmail(ds.Contacts[0].Email) || EmailManager.IsValidEmail(ds.Contacts[0].Email2))
                        {
                            // File.Delete("*.eml");  //Διαγράφει τα eml αρχεία που έχουν ήδη δημιουργηθεί.

                            long emailTemplateId = Convert.ToInt64(this.emailTemplatesDDL.SelectedItemsValue[0]);
                            MentalViewDataSet.EmailTemplatesRow emailTemplatesRow = Data.Business.EmailTemplatesBusiness.GetEmailTemplateById(emailTemplateId).EmailTemplates[0];

                            var mailMessage = new MailMessage();
                            mailMessage.From = new MailAddress("<EMAIL>");
                            mailMessage.To.Add(new MailAddress(ds.Contacts[0].Email));
                            mailMessage.To.Add(new MailAddress(ds.Contacts[0].Email2));
                            mailMessage.Subject = emailTemplatesRow.Subject;
                            mailMessage.IsBodyHtml = true;
                            mailMessage.Body = emailTemplatesRow.Body;
                            mailMessage.Headers.Add("X-Unsent", "1");
                            //mailMessage.Attachments.Add(new Attachment("C://Myfile.pdf"));

                            var filename = HttpContext.Current.Server.MapPath("~") + Guid.NewGuid().ToString() + ".eml";

                            //save the MailMessage to the filesystem
                            mailMessage.Save(filename);

                            Response.Clear();
                            Response.ContentType = @"application\octet-stream";
                            System.IO.FileInfo file = new System.IO.FileInfo(filename);
                            using (Stream str = new FileStream(filename, FileMode.Open, FileAccess.Read))
                            {
                                MemoryStream ms = new MemoryStream();
                                byte[] bytes = new byte[file.Length];
                                str.Read(bytes, 0, (int)file.Length);
                                ms.Write(bytes, 0, (int)file.Length);

                                Response.ClearHeaders();
                                Response.ContentType = "application/" + "eml";
                                Response.AddHeader("Accept-Header", ms.Length.ToString());
                                Response.AddHeader("Content-Disposition", "Inline" + "; filename=" + new FileInfo(filename).Name);
                                Response.AddHeader("Content-Length", ms.Length.ToString());
                                //Response.ContentEncoding = System.Text.Encoding.Default;

                                Response.BinaryWrite(ms.ToArray());
                                Response.Flush();
                                Response.End();
                            }
                            //1η λύση
                            //Open the file with the default associated application registered on the local machine
                            //Process.Start(filename);
                            //System.Diagnostics.Process p = new System.Diagnostics.Process();
                            //p.StartInfo.FileName = filename;
                            //p.StartInfo.UseShellExecute = false;
                            //p.Start();
                            //p.WaitForExit();

                            //2η λύση
                            //Process.Start(@"mailto:" + ds.Contacts[0].Email + "?subject=" + emailTemplatesRow.Subject + "&body=" + emailTemplatesRow.Body);

                            Thread.Sleep(1000);

                            this.emailTemplatesDDL.ClearSelection();
                            this.emailTemplatesDialog.Visible = false;
                            File.Delete(filename);
                        }
                        else
                        {
                            ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EmailIsInvalidMessage").ToString(), ServerMessageButtons.Ok);
                        }
                    }
                    else
                    {
                        ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EmailIsEmptyMessage").ToString(), ServerMessageButtons.Ok);
                    }
                }
                else
                {
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, GetLocalResourceObject("EmailTemplateRequiredMessage").ToString(), ServerMessageButtons.Ok);
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void emailTemplatesGrid_ServerEditRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                Int64 contactEmailTemplateId = Convert.ToInt64(((Dictionary<string, object>)e.Arguments["previousData"])["ContactEmailTemplateId"]);
                MentalViewDataSet.ContactEmailTemplatesRow contactEmailTemplateRow = ds.ContactEmailTemplates.FindByContactEmailTemplateId(contactEmailTemplateId);
                contactEmailTemplateRow.EmailTemplateTitle = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateTitle"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateTitle"].ToString();
                if (((Dictionary<string, object>)e.Arguments["data"])["CreateDate"] != null)
                {
                    contactEmailTemplateRow.Date = DateTime.Parse(((Dictionary<string, object>)e.Arguments["data"])["CreateDate"].ToString());
                }
                contactEmailTemplateRow.Status = ((Dictionary<string, object>)e.Arguments["data"])["Status"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Status"].ToString();

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void emailTemplatesGrid_ServerAddRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                MentalViewDataSet.ContactEmailTemplatesRow contactEmailTemplatesRow = ds.ContactEmailTemplates.NewContactEmailTemplatesRow();
                contactEmailTemplatesRow.ContactId = ds.Contacts[0].ContactId;
                contactEmailTemplatesRow.EmailTemplateTitle = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateTitle"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateTitle"].ToString();
                if (((Dictionary<string, object>)e.Arguments["data"])["Date"] != null)
                {
                    contactEmailTemplatesRow.Date = DateTime.Parse(((Dictionary<string, object>)e.Arguments["data"])["Date"].ToString());
                }
                contactEmailTemplatesRow.Status = ((Dictionary<string, object>)e.Arguments["data"])["Status"] == null ? "" : ((Dictionary<string, object>)e.Arguments["data"])["Status"].ToString();
                ds.ContactEmailTemplates.AddContactEmailTemplatesRow(contactEmailTemplatesRow);
                ViewState["Contact"] = ds;

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        protected void emailTemplatesGrid_ServerDeleteRow(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                string contactEmailTemplateId = ((Dictionary<string, object>)e.Arguments["data"])["ContactEmailTemplateId"].ToString();
                ((Main)this.Master).ServerMessage.ShowModal(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.DeleteConfirmationMessage, ServerMessageButtons.YesNo, "DeleteContactEmailTemplate", contactEmailTemplateId);

                //ViewState["Contact"] = ds;
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }




        protected void printTextBtn_Click(object sender, EventArgs e)
        {
            ScriptManager.RegisterStartupScript(this, this.GetType(), "PrintScript", "printDialogText();", true);
        }

        protected void chatGptDiagnosisAndTherapyBtn_Click(object sender, EventArgs e)
        {
            try
            {
                Dictionary<string, object> userData = JsonConvert.DeserializeObject<Dictionary<string, object>>(CookieHandler.GetAuthCookie(Page).UserData);
                Int64 tenantId = Convert.ToInt64(userData["TenantId"]);

                Data.MentalViewDataSet ds = (Data.MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;


                //Loads an existing Word document
                //WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReport(ds, tenantId, this.printContactDataChkBox.Checked, this.printContactQuestionnairesChkBox.Checked, printContactAppointmentsChkBox.Checked);
                WordDocument wordDocument = new Reporting.ReportsFactory().PrepareContactReportNew(ds, tenantId, true, false, false, false);
                string contactData = wordDocument.GetText();

                string systemPrompt = "Είσαι ένας ψυχολόγος-θεραπευτής.";
                string userPrompt = "Με βάση τα παρακάτω ιατρικά στοιχεία δώσε μου α) μια λεπτομερής διάγνωση και β) μια προτεινόμενη θεραπεία.";
                userPrompt += contactData;

                OpenAIResponse result = SendRequestToChatGPT(systemPrompt, userPrompt);

                aiResponseTxtBox.Text = result.Message;
                infoLabel.Text = "Συνολικά tokens " + result.TotalTokens.ToString();

                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }
        }

        private OpenAIResponse SendRequestToChatGPT(string systemPrompt, string userPrompt)
        {
            try
            {
                //Thread.Sleep(3000);
                //return new OpenAIResponse();

                //string apiKey = "********************************************************************************************************************************************************************";
                string apiKey = "********************************************************************************************************************************************************************";
                string endpoint = "https://api.openai.com/v1/chat/completions";
                var requestBody = new
                {
                    model = "gpt-3.5-turbo",  // or "gpt-3.5-turbo"
                    messages = new[]
                    {
                        new { role = "system", content = systemPrompt},
                        new { role = "user", content = userPrompt }
                    },
                    max_tokens = 2000
                };

                string jsonPayload = JsonConvert.SerializeObject(requestBody);
                using (var client = new HttpClient())
                {
                    client.DefaultRequestHeaders.Add("Authorization", $"Bearer {apiKey}");

                    var response = client.PostAsync(
                        endpoint,
                        new StringContent(jsonPayload, Encoding.UTF8, "application/json")
                    ).Result;

                    string responseBody = response.Content.ReadAsStringAsync().Result;
                    var responseJson = JsonConvert.DeserializeObject(responseBody);
                    Console.WriteLine(responseBody);
                    Console.WriteLine(responseJson);

                    try
                    {
                        OpenAIResponse result = ParseOpenAIResponse(responseBody);
                        return result;
                    }
                    catch (Exception exp)
                    {
                        Data.ExceptionLogger.LogException(exp);
                        OpenAIResponse failedResponse = new OpenAIResponse();
                        failedResponse.Message = GetLocalResourceObject("ChatCptCommunicationError").ToString();
                        failedResponse.TotalTokens = 0;
                        return failedResponse;
                    }
                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
            }

            return null;
        }

        public class OpenAIResponse
        {
            public string Message { get; set; } = string.Empty;
            public int TotalTokens { get; set; } = 0;
        }

        public OpenAIResponse ParseOpenAIResponse(string jsonResponse)
        {
            var response = JsonConvert.DeserializeObject<dynamic>(jsonResponse);

            return new OpenAIResponse
            {
                Message = response.choices[0].message.content,
                TotalTokens = response.usage.total_tokens
            };
        }

        protected void emailTemplatesGrid_ServerCommandButtonClick(object sender, GridEventArgs e)
        {
            try
            {
                Data.MentalViewDataSet ds = (MentalViewDataSet)ViewState["Contact"];
                this.GetDataFromUIControls(ref ds);
                ViewState["Contact"] = ds;

                if (e.EventType == "commandButtonClick")
                {
                    if (e.Arguments["commandType"].ToString() == "OpenTemplate")
                    {
                        string emailTemplateId = ((Dictionary<string, object>)e.Arguments["data"])["EmailTemplateId"].ToString();
                        Response.Redirect("~/EmailTemplate?EmailTemplateId=" + emailTemplateId);
                    }
                }

                // this.FillContactsGrid();
                this.SetDataOnUIControls(ds);
            }
            catch (Exception exp)
            {
                if (exp.GetType() != typeof(ThreadAbortException))
                {
                    Data.ExceptionLogger.LogException(exp);
                    ((Main)this.Master).ServerMessage.Show(Resources.GlobalResources.ApplicationTitle, Resources.GlobalResources.ExceptionOccuredMessage, ServerMessageButtons.Ok);
                }
            }
        }
    }
}