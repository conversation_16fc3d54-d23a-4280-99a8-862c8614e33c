﻿using Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using static Data.MentalViewDataSet;

namespace WebUI
{
    public partial class Notifications : System.Web.UI.Page
    {
        protected void Page_Load(object sender, EventArgs e)
        {
            int appointmentsCount = 0;

            try
            {
                if (this.Request.Params[0] == "ContactAppointmentsEmailNotification")
                {
                    //Reads the Tenants from the DB
                    MentalViewDataSet tenantsDS = Business.TenantsBusiness.GetAllTenants();

                    int emailsSent = 0;
                    int sentEmailLimit = 50;  //Είναι το όριο από email που θα στείλει (για διάφορους Tenants) και έπειτα θα σταματήσει για να μη θεωρηθεί spamming,  
                    //ένω τα υπόλοιπα email θα σταλούν σε επόμενο execution της σελίδας.

                    //Foreach Tenant
                    foreach (MentalViewDataSet.TenantsRow tenantsRow in tenantsDS.Tenants)
                    {
                        //Βρίσκει όλα τα appointments για τα οποία δεν έχει σταλεί email στον πελάτη.
                        MentalViewDataSet appointmentsForEmailDS = Business.AppointmentsBusiness.GetAppointmentForContactsEmailNotification(tenantsRow.TenantId, DateTime.Now.AddDays(1).Date, DateTime.Now.AddDays(1).Date);
                        appointmentsCount = appointmentsForEmailDS.Appointments.Count();
                        
                        //if (appointmentsCount > 30)
                        //{
                        //    this.result.Text += "<br/><br/>Total Appointments: " + appointmentsCount.ToString();
                        //}

                        foreach (MentalViewDataSet.AppointmentsRow appointmentsRow in appointmentsForEmailDS.Appointments)
                        {
                            if (emailsSent < sentEmailLimit)
                            {
                                UsersRow userRow = appointmentsForEmailDS.Users.FindByUserId(appointmentsRow.UserId);
                                ContactsRow contactRow = appointmentsForEmailDS.Contacts.FindByContactId(appointmentsRow.ContactId);

                                string host = System.Configuration.ConfigurationManager.AppSettings["EmailHost"];
                                int port = Convert.ToInt32(System.Configuration.ConfigurationManager.AppSettings["EmailPort"]);
                                string username = System.Configuration.ConfigurationManager.AppSettings["EmailAcountUsername"];  //Θέλει αλλαγή σε NoReply
                                string password = System.Configuration.ConfigurationManager.AppSettings["EmailAcountPassword"];  //Θέλει αλλαγή σε NoReply
                                bool useDefaultCredentials = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailUseDefaultCredentials"]);
                                bool enableSsl = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EmailEnableSsl"]);
                                string senderEmail = System.Configuration.ConfigurationManager.AppSettings["SenderEmail"];  //Θέλει αλλαγή σε NoReply
                                string senderName = System.Configuration.ConfigurationManager.AppSettings["SenderName"];  
                                string htmlSignature = System.Configuration.ConfigurationManager.AppSettings["EmailHtmlSignature"];

                                EmailManager emailManager = new EmailManager(host, port, username, password, useDefaultCredentials, enableSsl, senderEmail, senderName, htmlSignature);
                                emailManager.SendAppointmentReminderEmailToContact(userRow, appointmentsRow, contactRow);

                                emailsSent++;

                                appointmentsRow.ContactEmailNotificationSent = true;
                                Business.SaveAllData(appointmentsForEmailDS);
                                Thread.Sleep(500); //Βάζουμε μια καθυστέρηση για να μη θεωρηθεί spamming.
                            }
                            else
                            {
                                continue;
                            }
                        }
                    }
                    this.result.Text += $"<br/><br/>Emails sent: {emailsSent} of {appointmentsCount} (email limit per execution: {sentEmailLimit})";

                }
            }
            catch (Exception exp)
            {
                Data.ExceptionLogger.LogException(exp);
                //this.serverMessage.ShowModal(Resources.GlobalResource.ApplicationTitle, Resources.GlobalResource.ExceptionOccuredMessage, ServerMessageButtons.Ok, "");

                this.result.Text += "<br/><br/>" + exp.Message;
            }
            finally
            {
                //this.result.Text += "<br/><br/>(in Finaly) Total Appointments: " + appointmentsCount.ToString();
            }
        }
    }
}