@page "/login"
@layout LoginLayout
@using Admin.Components.Layout
@using Admin.Services
@using Microsoft.AspNetCore.Components.Authorization
@using Syncfusion.Blazor.Buttons
@using Syncfusion.Blazor.Inputs
@using System.ComponentModel.DataAnnotations

@inject IAuthenticationService AuthService
@inject NavigationManager Navigation
@inject AuthenticationStateProvider AuthStateProvider

<PageTitle>@Resources.LoginResources.Title - MentalView Admin</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <h2 class="text-center mb-4">
                <i class="fa-solid fa-user-shield me-2"></i>
                @Resources.LoginResources.WelcomeMessage
            </h2>
            <p class="text-center text-muted mb-4">@Resources.LoginResources.PleaseLogin</p>
        </div>

        <div class="login-form">
            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
                <DataAnnotationsValidator />
                
                <div class="form-group mb-3">
                    <label for="username" class="form-label">
                        <i class="fa-solid fa-user me-2"></i>
                        @Resources.LoginResources.Username
                    </label>
                    <SfTextBox @bind-Value="@loginModel.Username" 
                               Placeholder="@Resources.LoginResources.Username"
                               CssClass="form-control"
                               ShowClearButton="true">
                    </SfTextBox>
                    <ValidationMessage For="@(() => loginModel.Username)" />
                </div>

                <div class="form-group mb-4">
                    <label for="password" class="form-label">
                        <i class="fa-solid fa-lock me-2"></i>
                        @Resources.LoginResources.Password
                    </label>
                    <SfTextBox @bind-Value="@loginModel.Password" 
                               Type="InputType.Password"
                               Placeholder="@Resources.LoginResources.Password"
                               CssClass="form-control"
                               ShowClearButton="true">
                    </SfTextBox>
                    <ValidationMessage For="@(() => loginModel.Password)" />
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger mb-3" role="alert">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        @errorMessage
                    </div>
                }

                <div class="d-grid">
                    <SfButton Type="ButtonType.Submit" 
                              IsPrimary="true" 
                              CssClass="btn-lg"
                              Disabled="@isLoading">
                        @if (isLoading)
                        {
                            <i class="fa-solid fa-spinner fa-spin me-2"></i>
                        }
                        else
                        {
                            <i class="fa-solid fa-sign-in-alt me-2"></i>
                        }
                        @Resources.LoginResources.LoginButton
                    </SfButton>
                </div>
            </EditForm>
        </div>

        <div class="login-footer mt-4">
            <div class="text-center text-muted">
                <small>
                    <i class="fa-solid fa-info-circle me-1"></i>
                    Demo credentials: admin/admin123 or superadmin/super123
                </small>
            </div>
        </div>
    </div>
</div>

<style>
    .login-container {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
    }

    .login-card {
        background: white;
        border-radius: 15px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        padding: 40px;
        width: 100%;
        max-width: 450px;
        border: 1px solid #e9ecef;
    }

    .login-header h2 {
        color: #495057;
        font-weight: 600;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
        margin-bottom: 8px;
    }

    .login-footer {
        border-top: 1px solid #e9ecef;
        padding-top: 20px;
    }

    .alert {
        border-radius: 8px;
    }

    .btn-lg {
        padding: 12px 24px;
        font-size: 16px;
        border-radius: 8px;
    }

    .form-control {
        border-radius: 8px;
        border: 1px solid #ced4da;
        padding: 12px 16px;
    }

    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
</style>

@code {
    private LoginModel loginModel = new();
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    protected override async Task OnInitializedAsync()
    {
        // Check if user is already authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            Navigation.NavigateTo("/tenants");
        }
    }

    private async Task HandleLogin()
    {
        isLoading = true;
        errorMessage = string.Empty;
        StateHasChanged();

        try
        {
            var success = await AuthService.LoginAsync(loginModel.Username, loginModel.Password);
            
            if (success)
            {
                Navigation.NavigateTo("/tenants");
            }
            else
            {
                errorMessage = Resources.LoginResources.InvalidCredentials;
            }
        }
        catch (Exception ex)
        {
            errorMessage = "An error occurred during login. Please try again.";
            // Log the exception in a real application
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    public class LoginModel
    {
        [Required(ErrorMessage = "Username is required")]
        public string Username { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }
}
